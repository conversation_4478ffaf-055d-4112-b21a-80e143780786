#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GetShell 使用示例
演示如何使用GetShell进行渗透测试
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config import Config
from core.logger import Logger
from core.scanner import Scanner
from utils.banner import print_banner


async def example_single_target():
    """示例1: 扫描单个目标"""
    print("\n" + "="*50)
    print("示例1: 扫描单个目标")
    print("="*50)
    
    # 配置
    config = Config()
    config.targets = ['127.0.0.1']  # 本地测试
    config.ports = [80, 443, 8080, 8000]
    config.threads = 10
    config.timeout = 5
    config.verbose = True
    config.exploit_mode = False  # 仅扫描，不利用
    
    # 初始化
    logger = Logger(config)
    scanner = Scanner(config, logger)
    
    # 执行扫描
    results = await scanner.scan_all()
    
    print(f"扫描完成，共扫描 {len(results['targets'])} 个目标")


async def example_network_scan():
    """示例2: 网段扫描"""
    print("\n" + "="*50)
    print("示例2: 网段扫描")
    print("="*50)
    
    # 配置
    config = Config()
    config.targets = ['192.168.1.0/28']  # 小网段测试
    config.ports = [80, 443]
    config.threads = 20
    config.timeout = 3
    config.fast_scan = True
    config.verbose = True
    
    # 初始化
    logger = Logger(config)
    scanner = Scanner(config, logger)
    
    # 解析目标
    parsed_targets = await scanner.target_parser.parse_targets(config.targets)
    print(f"解析出 {len(parsed_targets)} 个目标")
    
    # 限制目标数量（演示用）
    if len(parsed_targets) > 5:
        parsed_targets = parsed_targets[:5]
        print(f"限制为前 {len(parsed_targets)} 个目标进行演示")
    
    config.targets = parsed_targets
    results = await scanner.scan_all()
    
    print(f"扫描完成，共扫描 {len(results['targets'])} 个目标")


async def example_vulnerability_scan():
    """示例3: 漏洞扫描"""
    print("\n" + "="*50)
    print("示例3: 漏洞扫描")
    print("="*50)
    
    # 配置
    config = Config()
    config.targets = ['httpbin.org']  # 使用公开测试站点
    config.ports = [80, 443]
    config.threads = 5
    config.timeout = 10
    config.exploit_mode = False  # 仅检测，不利用
    config.verbose = True
    
    # 初始化
    logger = Logger(config)
    scanner = Scanner(config, logger)
    
    # 执行扫描
    results = await scanner.scan_all()
    
    # 统计结果
    total_vulns = 0
    for target_result in results['targets']:
        vulns = target_result.get('vulnerabilities', [])
        total_vulns += len(vulns)
        if vulns:
            print(f"目标 {target_result['target']} 发现 {len(vulns)} 个漏洞")
    
    print(f"总共发现 {total_vulns} 个漏洞")


async def example_webshell_test():
    """示例4: Webshell模板测试"""
    print("\n" + "="*50)
    print("示例4: Webshell模板测试")
    print("="*50)
    
    from modules.exploit_manager import ExploitManager
    
    config = Config()
    logger = Logger(config)
    exploit_manager = ExploitManager(config, logger)
    
    # 显示所有Webshell模板
    print("可用的Webshell模板:")
    for shell_type, templates in exploit_manager.webshell_templates.items():
        print(f"\n{shell_type.upper()} Webshell:")
        for template_name, template_content in templates.items():
            print(f"  - {template_name}: {len(template_content)} 字符")
            if template_name == 'simple':
                print(f"    内容预览: {template_content[:50]}...")
    
    # 生成示例报告
    example_webshells = [
        {
            'type': 'php',
            'url': 'http://example.com/shell.php',
            'method': 'file_upload',
            'filename': 'shell.php',
            'param': 'cmd'
        },
        {
            'type': 'jsp',
            'url': 'http://example.com/shell.jsp',
            'method': 'command_injection',
            'param': 'cmd',
            'password': 'getshell'
        }
    ]
    
    report = exploit_manager.generate_webshell_report(example_webshells)
    print(f"\n示例Webshell报告:\n{report}")


async def example_stealth_scan():
    """示例5: 隐蔽扫描"""
    print("\n" + "="*50)
    print("示例5: 隐蔽扫描")
    print("="*50)
    
    # 配置
    config = Config()
    config.targets = ['127.0.0.1']
    config.ports = [80, 443]
    config.threads = 5  # 较少线程
    config.timeout = 15  # 较长超时
    config.stealth_mode = True
    config.verbose = True
    
    # 初始化
    logger = Logger(config)
    scanner = Scanner(config, logger)
    
    print("使用隐蔽模式扫描...")
    print("- 较少并发连接")
    print("- 随机延迟")
    print("- 随机User-Agent")
    
    results = await scanner.scan_all()
    print(f"隐蔽扫描完成，共扫描 {len(results['targets'])} 个目标")


async def main():
    """主函数"""
    print_banner()
    
    print("GetShell 使用示例")
    print("以下示例演示了工具的各种使用方式")
    print("\n⚠️  注意: 这些示例仅用于学习和测试，请确保在授权环境中使用")
    
    try:
        # 示例1: 单个目标扫描
        await example_single_target()
        
        # 示例2: 网段扫描
        await example_network_scan()
        
        # 示例3: 漏洞扫描
        await example_vulnerability_scan()
        
        # 示例4: Webshell模板测试
        await example_webshell_test()
        
        # 示例5: 隐蔽扫描
        await example_stealth_scan()
        
        print("\n" + "="*50)
        print("所有示例执行完成!")
        print("="*50)
        
    except Exception as e:
        print(f"示例执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n示例被用户中断")
    except Exception as e:
        print(f"程序执行错误: {e}")
