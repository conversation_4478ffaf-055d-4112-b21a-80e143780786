#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
横幅显示模块
"""

import random
from datetime import datetime


def print_banner():
    """打印工具横幅"""
    
    banners = [
        """
╔═══════════════════════════════════════════════════════════════╗
║                          GetShell                             ║
║                     全域扫描工具 v1.0                          ║
║                                                               ║
║    专业的红队渗透测试工具，用于批量获取webshell                  ║
║                                                               ║
║    作者: 红队安全研究员                                        ║
║    时间: {}                                    ║
╚═══════════════════════════════════════════════════════════════╝
        """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
        
        """
    ██████╗ ███████╗████████╗███████╗██╗  ██╗███████╗██╗     ██╗     
    ██╔════╝ ██╔════╝╚══██╔══╝██╔════╝██║  ██║██╔════╝██║     ██║     
    ██║  ███╗█████╗     ██║   ███████╗███████║█████╗  ██║     ██║     
    ██║   ██║██╔══╝     ██║   ╚════██║██╔══██║██╔══╝  ██║     ██║     
    ╚██████╔╝███████╗   ██║   ███████║██║  ██║███████╗███████╗███████╗
     ╚═════╝ ╚══════╝   ╚═╝   ╚══════╝╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝
                                                                      
                        全域扫描工具 v1.0
                    专业的红队渗透测试工具
                      {}
        """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
        
        """
    ░██████╗░███████╗████████╗░██████╗██╗░░██╗███████╗██╗░░░░░██╗░░░░░
    ██╔════╝░██╔════╝╚══██╔══╝██╔════╝██║░░██║██╔════╝██║░░░░░██║░░░░░
    ██║░░██╗░█████╗░░░░░██║░░░╚█████╗░███████║█████╗░░██║░░░░░██║░░░░░
    ██║░░╚██╗██╔══╝░░░░░██║░░░░╚═══██╗██╔══██║██╔══╝░░██║░░░░░██║░░░░░
    ╚██████╔╝███████╗░░░██║░░░██████╔╝██║░░██║███████╗███████╗███████╗
    ░╚═════╝░╚══════╝░░░╚═╝░░░╚═════╝░╚═╝░░╚═╝╚══════╝╚══════╝╚══════╝
    
                        全域扫描工具 v1.0
                      批量获取webshell工具
                      {}
        """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    ]
    
    # 随机选择一个横幅
    banner = random.choice(banners)
    
    # 添加颜色
    colored_banner = f"\033[1;31m{banner}\033[0m"
    
    print(colored_banner)
    
    # 打印警告信息
    warning = """
\033[1;33m⚠️  警告信息 ⚠️\033[0m
\033[33m本工具仅供授权的安全测试使用！
请确保您有合法的授权才能使用此工具进行测试。
未经授权的使用可能违反相关法律法规。
使用者需要对自己的行为负责！\033[0m

\033[1;32m🎯 功能特性:\033[0m
\033[32m• 高效的异步端口扫描
• 智能的Web服务识别  
• 全面的漏洞检测
• 自动化Webshell部署
• 详细的扫描报告\033[0m

"""
    
    print(warning)


def print_scan_start(targets_count: int, ports_count: int, threads: int):
    """打印扫描开始信息"""
    print(f"\033[1;36m🚀 开始扫描任务\033[0m")
    print(f"\033[36m目标数量: {targets_count}")
    print(f"端口数量: {ports_count}")
    print(f"并发线程: {threads}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\033[0m")
    print("-" * 60)


def print_scan_progress(current: int, total: int, target: str = ""):
    """打印扫描进度"""
    percentage = (current / total) * 100
    progress_bar = "█" * int(percentage // 2) + "░" * (50 - int(percentage // 2))
    
    print(f"\r\033[36m进度: [{progress_bar}] {percentage:.1f}% ({current}/{total}) {target}\033[0m", end="")


def print_vulnerability_found(vuln_type: str, target: str):
    """打印发现漏洞信息"""
    print(f"\n\033[1;33m⚠️  发现漏洞: {vuln_type} - {target}\033[0m")


def print_webshell_success(shell_url: str, shell_type: str):
    """打印Webshell获取成功信息"""
    print(f"\n\033[1;31m🎯 Webshell获取成功!\033[0m")
    print(f"\033[31mURL: {shell_url}")
    print(f"类型: {shell_type}\033[0m")


def print_scan_complete(duration: float, webshells_count: int):
    """打印扫描完成信息"""
    print(f"\n\n\033[1;32m✅ 扫描完成!\033[0m")
    print(f"\033[32m耗时: {duration:.2f} 秒")
    print(f"获取Webshell数量: {webshells_count}\033[0m")
    
    if webshells_count > 0:
        print(f"\n\033[1;31m🎉 恭喜! 成功获取 {webshells_count} 个Webshell!\033[0m")
    
    print("-" * 60)
