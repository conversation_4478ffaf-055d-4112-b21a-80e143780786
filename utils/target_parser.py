#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目标解析模块
解析和扩展目标列表
"""

import asyncio
import ipaddress
import socket
import re
from typing import List, Set
import dns.resolver


class TargetParser:
    """目标解析器"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
    
    async def parse_targets(self, targets: List[str]) -> List[str]:
        """解析目标列表"""
        parsed_targets = set()
        
        for target in targets:
            target = target.strip()
            if not target:
                continue
            
            try:
                # 检查是否为CIDR网段
                if '/' in target:
                    cidr_targets = self._parse_cidr(target)
                    parsed_targets.update(cidr_targets)
                
                # 检查是否为IP范围
                elif '-' in target and self._is_ip_range(target):
                    range_targets = self._parse_ip_range(target)
                    parsed_targets.update(range_targets)
                
                # 检查是否为域名
                elif self._is_domain(target):
                    domain_targets = await self._resolve_domain(target)
                    parsed_targets.update(domain_targets)
                
                # 单个IP地址
                elif self._is_ip(target):
                    parsed_targets.add(target)
                
                else:
                    self.logger.warning(f"无法解析目标: {target}")
                    
            except Exception as e:
                self.logger.error(f"解析目标 {target} 失败: {e}")
        
        result = list(parsed_targets)
        self.logger.info(f"解析完成，共 {len(result)} 个目标")
        return result
    
    def _parse_cidr(self, cidr: str) -> List[str]:
        """解析CIDR网段"""
        try:
            network = ipaddress.ip_network(cidr, strict=False)
            targets = []
            
            # 限制网段大小，避免过大的网段
            if network.num_addresses > 65536:  # /16以上的网段
                self.logger.warning(f"网段 {cidr} 过大，跳过")
                return []
            
            for ip in network.hosts():
                targets.append(str(ip))
                
                # 限制单个网段的主机数量
                if len(targets) >= 10000:
                    self.logger.warning(f"网段 {cidr} 主机数量过多，限制为10000个")
                    break
            
            self.logger.debug(f"CIDR {cidr} 解析出 {len(targets)} 个目标")
            return targets
            
        except ValueError as e:
            self.logger.error(f"无效的CIDR格式: {cidr} - {e}")
            return []
    
    def _parse_ip_range(self, ip_range: str) -> List[str]:
        """解析IP范围 (例: ***********-*************)"""
        try:
            start_ip, end_ip = ip_range.split('-')
            start_ip = start_ip.strip()
            end_ip = end_ip.strip()
            
            # 转换为整数进行范围计算
            start_int = int(ipaddress.IPv4Address(start_ip))
            end_int = int(ipaddress.IPv4Address(end_ip))
            
            if start_int > end_int:
                self.logger.error(f"无效的IP范围: {ip_range}")
                return []
            
            # 限制范围大小
            if end_int - start_int > 10000:
                self.logger.warning(f"IP范围 {ip_range} 过大，限制为10000个")
                end_int = start_int + 10000
            
            targets = []
            for ip_int in range(start_int, end_int + 1):
                targets.append(str(ipaddress.IPv4Address(ip_int)))
            
            self.logger.debug(f"IP范围 {ip_range} 解析出 {len(targets)} 个目标")
            return targets
            
        except Exception as e:
            self.logger.error(f"解析IP范围失败 {ip_range}: {e}")
            return []
    
    async def _resolve_domain(self, domain: str) -> List[str]:
        """解析域名"""
        targets = []
        
        try:
            # 解析A记录
            try:
                answers = dns.resolver.resolve(domain, 'A')
                for answer in answers:
                    targets.append(str(answer))
            except:
                pass
            
            # 如果DNS解析失败，尝试socket解析
            if not targets:
                try:
                    ip = socket.gethostbyname(domain)
                    targets.append(ip)
                except:
                    pass
            
            # 尝试解析子域名（如果启用）
            if self.config.exploit_mode:
                subdomains = await self._enumerate_subdomains(domain)
                for subdomain in subdomains:
                    try:
                        ip = socket.gethostbyname(subdomain)
                        targets.append(ip)
                    except:
                        pass
            
            self.logger.debug(f"域名 {domain} 解析出 {len(targets)} 个IP")
            return list(set(targets))  # 去重
            
        except Exception as e:
            self.logger.error(f"解析域名失败 {domain}: {e}")
            return []
    
    async def _enumerate_subdomains(self, domain: str) -> List[str]:
        """枚举子域名"""
        subdomains = []
        
        # 常见子域名前缀
        common_subdomains = [
            'www', 'mail', 'ftp', 'admin', 'test', 'dev', 'staging',
            'api', 'app', 'web', 'secure', 'portal', 'login',
            'dashboard', 'panel', 'control', 'manage', 'system'
        ]
        
        for subdomain in common_subdomains:
            full_domain = f"{subdomain}.{domain}"
            try:
                # 简单的DNS查询测试
                socket.gethostbyname(full_domain)
                subdomains.append(full_domain)
            except:
                pass
        
        return subdomains
    
    def _is_ip(self, target: str) -> bool:
        """检查是否为有效IP地址"""
        try:
            ipaddress.ip_address(target)
            return True
        except ValueError:
            return False
    
    def _is_domain(self, target: str) -> bool:
        """检查是否为有效域名"""
        # 简单的域名格式检查
        domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        )
        return bool(domain_pattern.match(target))
    
    def _is_ip_range(self, target: str) -> bool:
        """检查是否为IP范围格式"""
        if '-' not in target:
            return False
        
        parts = target.split('-')
        if len(parts) != 2:
            return False
        
        return self._is_ip(parts[0].strip()) and self._is_ip(parts[1].strip())
    
    def validate_targets(self, targets: List[str]) -> List[str]:
        """验证目标列表"""
        valid_targets = []
        
        for target in targets:
            if self._is_ip(target):
                # 检查是否为私有IP（可选）
                try:
                    ip_obj = ipaddress.ip_address(target)
                    if ip_obj.is_private and not self.config.allow_private:
                        self.logger.warning(f"跳过私有IP: {target}")
                        continue
                    
                    if ip_obj.is_loopback:
                        self.logger.warning(f"跳过回环地址: {target}")
                        continue
                        
                    valid_targets.append(target)
                except:
                    pass
            else:
                valid_targets.append(target)
        
        return valid_targets
    
    def filter_targets(self, targets: List[str], exclude_list: List[str] = None) -> List[str]:
        """过滤目标列表"""
        if not exclude_list:
            return targets
        
        filtered_targets = []
        exclude_set = set(exclude_list)
        
        for target in targets:
            if target not in exclude_set:
                # 检查是否在排除的网段中
                excluded = False
                for exclude in exclude_list:
                    if '/' in exclude:  # CIDR格式
                        try:
                            network = ipaddress.ip_network(exclude, strict=False)
                            if ipaddress.ip_address(target) in network:
                                excluded = True
                                break
                        except:
                            pass
                
                if not excluded:
                    filtered_targets.append(target)
        
        return filtered_targets
