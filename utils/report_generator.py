#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器
生成各种格式的扫描报告
"""

import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
import csv
import os
from datetime import datetime
from typing import Dict, Any, List
from jinja2 import Template


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
    
    async def generate_reports(self, results: Dict[str, Any]):
        """生成所有格式的报告"""
        try:
            # JSON报告
            if self.config.output_format in ['json', 'all']:
                await self._generate_json_report(results)
            
            # XML报告
            if self.config.output_format in ['xml', 'all']:
                await self._generate_xml_report(results)
            
            # HTML报告
            if self.config.output_format in ['html', 'all']:
                await self._generate_html_report(results)
            
            # TXT报告
            if self.config.output_format in ['txt', 'all']:
                await self._generate_txt_report(results)
            
            # CSV报告
            if self.config.output_format in ['csv', 'all']:
                await self._generate_csv_report(results)
            
            # Webshell列表
            await self._generate_webshell_list(results)
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
    
    async def _generate_json_report(self, results: Dict[str, Any]):
        """生成JSON格式报告"""
        try:
            filename = os.path.join(self.config.output_dir, 
                                  f"scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"JSON报告已生成: {filename}")
            
        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {e}")
    
    async def _generate_xml_report(self, results: Dict[str, Any]):
        """生成XML格式报告"""
        try:
            filename = os.path.join(self.config.output_dir,
                                  f"scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml")
            
            # 创建根元素
            root = ET.Element("scan_report")
            
            # 扫描信息
            scan_info = ET.SubElement(root, "scan_info")
            for key, value in results['scan_info'].items():
                elem = ET.SubElement(scan_info, key)
                elem.text = str(value)
            
            # 目标结果
            targets = ET.SubElement(root, "targets")
            for target_result in results['targets']:
                target_elem = ET.SubElement(targets, "target")
                target_elem.set("name", target_result['target'])
                
                # 基本信息
                for key in ['timestamp', 'status']:
                    if key in target_result:
                        elem = ET.SubElement(target_elem, key)
                        elem.text = str(target_result[key])
                
                # 端口信息
                if target_result.get('ports'):
                    ports_elem = ET.SubElement(target_elem, "ports")
                    for port in target_result['ports']:
                        port_elem = ET.SubElement(ports_elem, "port")
                        port_elem.set("number", str(port['port']))
                        port_elem.set("status", port['status'])
                        port_elem.set("service", port.get('service', ''))
                
                # 漏洞信息
                if target_result.get('vulnerabilities'):
                    vulns_elem = ET.SubElement(target_elem, "vulnerabilities")
                    for vuln in target_result['vulnerabilities']:
                        vuln_elem = ET.SubElement(vulns_elem, "vulnerability")
                        vuln_elem.set("type", vuln['type'])
                        vuln_elem.set("severity", vuln['severity'])
                        vuln_elem.text = vuln.get('description', '')
                
                # Webshell信息
                if target_result.get('webshells'):
                    shells_elem = ET.SubElement(target_elem, "webshells")
                    for shell in target_result['webshells']:
                        shell_elem = ET.SubElement(shells_elem, "webshell")
                        shell_elem.set("type", shell['type'])
                        shell_elem.set("url", shell['url'])
                        shell_elem.set("method", shell['method'])
            
            # 格式化XML
            xml_str = minidom.parseString(ET.tostring(root)).toprettyxml(indent="  ")
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(xml_str)
            
            self.logger.info(f"XML报告已生成: {filename}")
            
        except Exception as e:
            self.logger.error(f"生成XML报告失败: {e}")
    
    async def _generate_html_report(self, results: Dict[str, Any]):
        """生成HTML格式报告"""
        try:
            filename = os.path.join(self.config.output_dir,
                                  f"scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
            
            # HTML模板
            html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GetShell 扫描报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .summary { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .target { background: white; margin-bottom: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .target-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #dee2e6; }
        .target-content { padding: 15px; }
        .vulnerability { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .vulnerability.high { background: #f8d7da; border-color: #f5c6cb; }
        .vulnerability.critical { background: #d1ecf1; border-color: #bee5eb; }
        .webshell { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .port { display: inline-block; background: #e9ecef; padding: 5px 10px; margin: 2px; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 GetShell 扫描报告</h1>
        <p>生成时间: {{ scan_info.end_time }}</p>
    </div>
    
    <div class="summary">
        <h2>📊 扫描摘要</h2>
        <table>
            <tr><td>扫描目标数量</td><td>{{ scan_info.targets_count }}</td></tr>
            <tr><td>成功扫描数量</td><td>{{ scan_info.success_count }}</td></tr>
            <tr><td>获取Webshell数量</td><td class="success">{{ scan_info.webshells_count }}</td></tr>
            <tr><td>扫描耗时</td><td>{{ scan_info.duration }} 秒</td></tr>
        </table>
    </div>
    
    {% for target in targets %}
    <div class="target">
        <div class="target-header">
            <h3>🎯 {{ target.target }}</h3>
            <span class="{% if target.status == 'success' %}success{% else %}error{% endif %}">
                {{ target.status }}
            </span>
        </div>
        <div class="target-content">
            {% if target.ports %}
            <h4>🔌 开放端口</h4>
            {% for port in target.ports %}
            <span class="port">{{ port.port }}/{{ port.protocol }} ({{ port.service }})</span>
            {% endfor %}
            {% endif %}
            
            {% if target.vulnerabilities %}
            <h4>⚠️ 发现漏洞</h4>
            {% for vuln in target.vulnerabilities %}
            <div class="vulnerability {{ vuln.severity.lower() }}">
                <strong>{{ vuln.type }}</strong> - {{ vuln.severity }}
                <br>{{ vuln.description }}
                {% if vuln.url %}<br><small>URL: {{ vuln.url }}</small>{% endif %}
            </div>
            {% endfor %}
            {% endif %}
            
            {% if target.webshells %}
            <h4>🎉 获取的Webshell</h4>
            {% for shell in target.webshells %}
            <div class="webshell">
                <strong>{{ shell.type.upper() }} Webshell</strong>
                <br>URL: <a href="{{ shell.url }}" target="_blank">{{ shell.url }}</a>
                <br>方法: {{ shell.method }}
                {% if shell.password %}<br>密码: {{ shell.password }}{% endif %}
            </div>
            {% endfor %}
            {% endif %}
        </div>
    </div>
    {% endfor %}
    
    <div class="summary">
        <p><small>报告由 GetShell v1.0 生成 | 仅供授权安全测试使用</small></p>
    </div>
</body>
</html>
            """
            
            template = Template(html_template)
            html_content = template.render(
                scan_info=results['scan_info'],
                targets=results['targets']
            )
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告已生成: {filename}")
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
    
    async def _generate_txt_report(self, results: Dict[str, Any]):
        """生成TXT格式报告"""
        try:
            filename = os.path.join(self.config.output_dir,
                                  f"scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("GetShell 扫描报告\n")
                f.write("=" * 60 + "\n\n")
                
                # 扫描摘要
                f.write("扫描摘要:\n")
                f.write("-" * 30 + "\n")
                for key, value in results['scan_info'].items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # 目标详情
                for target_result in results['targets']:
                    f.write(f"目标: {target_result['target']}\n")
                    f.write("-" * 40 + "\n")
                    f.write(f"状态: {target_result['status']}\n")
                    f.write(f"时间: {target_result['timestamp']}\n")
                    
                    if target_result.get('ports'):
                        f.write("开放端口:\n")
                        for port in target_result['ports']:
                            f.write(f"  {port['port']}/{port['protocol']} ({port.get('service', 'unknown')})\n")
                    
                    if target_result.get('vulnerabilities'):
                        f.write("发现漏洞:\n")
                        for vuln in target_result['vulnerabilities']:
                            f.write(f"  [{vuln['severity']}] {vuln['type']}\n")
                            f.write(f"    {vuln.get('description', '')}\n")
                    
                    if target_result.get('webshells'):
                        f.write("获取的Webshell:\n")
                        for shell in target_result['webshells']:
                            f.write(f"  {shell['type'].upper()}: {shell['url']}\n")
                            f.write(f"    方法: {shell['method']}\n")
                            if shell.get('password'):
                                f.write(f"    密码: {shell['password']}\n")
                    
                    f.write("\n")
            
            self.logger.info(f"TXT报告已生成: {filename}")
            
        except Exception as e:
            self.logger.error(f"生成TXT报告失败: {e}")
    
    async def _generate_csv_report(self, results: Dict[str, Any]):
        """生成CSV格式报告"""
        try:
            filename = os.path.join(self.config.output_dir,
                                  f"scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入表头
                writer.writerow([
                    'Target', 'Status', 'Open_Ports', 'Vulnerabilities', 
                    'Webshells', 'Timestamp'
                ])
                
                # 写入数据
                for target_result in results['targets']:
                    ports = ';'.join([f"{p['port']}/{p['protocol']}" 
                                    for p in target_result.get('ports', [])])
                    vulns = ';'.join([f"{v['type']}({v['severity']})" 
                                    for v in target_result.get('vulnerabilities', [])])
                    shells = ';'.join([f"{s['type']}:{s['url']}" 
                                     for s in target_result.get('webshells', [])])
                    
                    writer.writerow([
                        target_result['target'],
                        target_result['status'],
                        ports,
                        vulns,
                        shells,
                        target_result['timestamp']
                    ])
            
            self.logger.info(f"CSV报告已生成: {filename}")
            
        except Exception as e:
            self.logger.error(f"生成CSV报告失败: {e}")
    
    async def _generate_webshell_list(self, results: Dict[str, Any]):
        """生成Webshell列表文件"""
        try:
            filename = os.path.join(self.config.output_dir,
                                  f"webshells_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            
            webshells = []
            for target_result in results['targets']:
                for shell in target_result.get('webshells', []):
                    webshells.append(shell)
            
            if webshells:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("GetShell 获取的Webshell列表\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for i, shell in enumerate(webshells, 1):
                        f.write(f"{i}. {shell['type'].upper()} Webshell\n")
                        f.write(f"   URL: {shell['url']}\n")
                        f.write(f"   方法: {shell['method']}\n")
                        if shell.get('password'):
                            f.write(f"   密码: {shell['password']}\n")
                        if shell.get('param'):
                            f.write(f"   参数: {shell['param']}\n")
                        f.write("\n")
                
                self.logger.info(f"Webshell列表已生成: {filename}")
            
        except Exception as e:
            self.logger.error(f"生成Webshell列表失败: {e}")
