#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GetShell - 全域扫描工具
专业的红队渗透测试工具，用于批量获取webshell
作者: 红队安全研究员
版本: 1.0.0
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scanner import Scanner
from core.config import Config
from core.logger import setup_logger
from utils.banner import print_banner


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="GetShell - 全域扫描工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py -t ***********/24 -p 80,443,8080 --threads 50
  python main.py -f targets.txt --full-scan --output results/
  python main.py -t example.com --exploit-mode --webshell php
        """
    )
    
    # 目标参数
    target_group = parser.add_mutually_exclusive_group(required=True)
    target_group.add_argument('-t', '--target', 
                             help='目标IP/域名/CIDR (例: *********** 或 ***********/24)')
    target_group.add_argument('-f', '--file', 
                             help='目标文件路径 (每行一个目标)')
    
    # 扫描参数
    parser.add_argument('-p', '--ports', default='80,443,8080,8000,3000,5000',
                       help='端口列表 (默认: 80,443,8080,8000,3000,5000)')
    parser.add_argument('--threads', type=int, default=50,
                       help='并发线程数 (默认: 50)')
    parser.add_argument('--timeout', type=int, default=10,
                       help='连接超时时间 (默认: 10秒)')
    
    # 扫描模式
    parser.add_argument('--full-scan', action='store_true',
                       help='全端口扫描 (1-65535)')
    parser.add_argument('--fast-scan', action='store_true',
                       help='快速扫描模式')
    parser.add_argument('--stealth', action='store_true',
                       help='隐蔽扫描模式')
    
    # 漏洞利用
    parser.add_argument('--exploit-mode', action='store_true',
                       help='启用漏洞利用模式')
    parser.add_argument('--webshell', choices=['php', 'jsp', 'aspx', 'all'],
                       default='php', help='Webshell类型 (默认: php)')
    
    # 输出参数
    parser.add_argument('-o', '--output', default='results',
                       help='输出目录 (默认: results)')
    parser.add_argument('--format', choices=['json', 'xml', 'html', 'txt'],
                       default='json', help='输出格式 (默认: json)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='详细输出模式')
    parser.add_argument('--debug', action='store_true',
                       help='调试模式')
    
    return parser.parse_args()


async def main():
    """主函数"""
    # 显示横幅
    print_banner()
    
    # 解析参数
    args = parse_arguments()
    
    # 设置日志
    logger = setup_logger(
        verbose=args.verbose,
        debug=args.debug,
        output_dir=args.output
    )
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 初始化配置
    config = Config(args)
    
    # 初始化扫描器
    scanner = Scanner(config, logger)
    
    try:
        logger.info("开始扫描任务...")
        
        # 执行扫描
        results = await scanner.run()
        
        logger.info(f"扫描完成! 发现 {len(results)} 个目标")
        logger.info(f"结果已保存到: {args.output}")
        
    except KeyboardInterrupt:
        logger.warning("用户中断扫描")
        sys.exit(1)
    except Exception as e:
        logger.error(f"扫描过程中发生错误: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
