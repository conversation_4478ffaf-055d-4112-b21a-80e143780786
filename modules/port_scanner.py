#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端口扫描模块
高效的异步端口扫描器
"""

import asyncio
import socket
import time
from typing import List, Dict, Any, Optional
import random


class PortScanner:
    """端口扫描器"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        
        # 常见服务端口映射
        self.service_map = {
            21: 'ftp', 22: 'ssh', 23: 'telnet', 25: 'smtp',
            53: 'dns', 80: 'http', 110: 'pop3', 143: 'imap',
            443: 'https', 993: 'imaps', 995: 'pop3s',
            3306: 'mysql', 5432: 'postgresql', 6379: 'redis',
            1433: 'mssql', 1521: 'oracle', 27017: 'mongodb',
            8080: 'http-proxy', 8000: 'http-alt', 8443: 'https-alt',
            3000: 'http-dev', 5000: 'http-dev', 8888: 'http-alt',
            9000: 'http-alt', 8008: 'http-alt', 8081: 'http-alt',
            8082: 'http-alt', 9090: 'http-alt', 7001: 'weblogic',
            7002: 'weblogic', 8001: 'weblogic', 8002: 'weblogic'
        }
    
    async def scan_target(self, target: str, ports: List[int]) -> List[Dict[str, Any]]:
        """扫描目标的端口"""
        self.logger.debug(f"开始扫描 {target} 的 {len(ports)} 个端口")
        
        # 如果启用隐蔽模式，随机化端口顺序和添加延迟
        if self.config.stealth_mode:
            ports = ports.copy()
            random.shuffle(ports)
        
        # 创建扫描任务
        semaphore = asyncio.Semaphore(min(50, self.config.threads))  # 限制端口扫描并发数
        tasks = []
        
        for port in ports:
            task = asyncio.create_task(self._scan_port(semaphore, target, port))
            tasks.append(task)
            
            # 隐蔽模式下添加随机延迟
            if self.config.stealth_mode:
                await asyncio.sleep(random.uniform(0.01, 0.1))
        
        # 执行扫描
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤开放的端口
        open_ports = []
        for result in results:
            if isinstance(result, dict) and result.get('status') == 'open':
                open_ports.append(result)
        
        # 按端口号排序
        open_ports.sort(key=lambda x: x['port'])
        
        self.logger.debug(f"{target} 发现 {len(open_ports)} 个开放端口")
        return open_ports
    
    async def _scan_port(self, semaphore: asyncio.Semaphore, 
                        target: str, port: int) -> Dict[str, Any]:
        """扫描单个端口"""
        async with semaphore:
            try:
                # TCP连接扫描
                future = asyncio.open_connection(target, port)
                reader, writer = await asyncio.wait_for(
                    future, timeout=self.config.timeout
                )
                
                # 获取服务信息
                service = self._identify_service(port, reader, writer)
                
                # 关闭连接
                writer.close()
                await writer.wait_closed()
                
                return {
                    'port': port,
                    'status': 'open',
                    'service': service,
                    'protocol': 'tcp'
                }
                
            except (asyncio.TimeoutError, ConnectionRefusedError, 
                   OSError, Exception):
                return {
                    'port': port,
                    'status': 'closed',
                    'service': '',
                    'protocol': 'tcp'
                }
    
    def _identify_service(self, port: int, reader, writer) -> str:
        """识别端口服务"""
        # 首先检查已知端口映射
        if port in self.service_map:
            return self.service_map[port]
        
        # 尝试服务识别（简单版本）
        try:
            # 对于HTTP服务，尝试发送HTTP请求
            if port in [80, 8080, 8000, 3000, 5000, 8008, 8888, 9000]:
                return 'http'
            elif port in [443, 8443]:
                return 'https'
            else:
                return 'unknown'
        except:
            return 'unknown'
    
    async def scan_udp_port(self, target: str, port: int) -> Dict[str, Any]:
        """UDP端口扫描（可选功能）"""
        try:
            # 创建UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.config.timeout)
            
            # 发送UDP数据包
            sock.sendto(b'test', (target, port))
            
            # 尝试接收响应
            try:
                data, addr = sock.recvfrom(1024)
                sock.close()
                return {
                    'port': port,
                    'status': 'open',
                    'service': self.service_map.get(port, 'unknown'),
                    'protocol': 'udp'
                }
            except socket.timeout:
                sock.close()
                return {
                    'port': port,
                    'status': 'open|filtered',
                    'service': self.service_map.get(port, 'unknown'),
                    'protocol': 'udp'
                }
                
        except Exception as e:
            return {
                'port': port,
                'status': 'closed',
                'service': '',
                'protocol': 'udp',
                'error': str(e)
            }
    
    async def advanced_scan(self, target: str, ports: List[int]) -> List[Dict[str, Any]]:
        """高级扫描模式（包含服务版本检测）"""
        results = await self.scan_target(target, ports)
        
        # 对开放端口进行服务版本检测
        for port_info in results:
            if port_info['status'] == 'open':
                version_info = await self._detect_service_version(
                    target, port_info['port']
                )
                port_info.update(version_info)
        
        return results
    
    async def _detect_service_version(self, target: str, port: int) -> Dict[str, str]:
        """检测服务版本信息"""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(target, port),
                timeout=self.config.timeout
            )
            
            version_info = {'version': '', 'banner': ''}
            
            # 尝试读取banner
            try:
                banner = await asyncio.wait_for(
                    reader.read(1024), timeout=2
                )
                if banner:
                    version_info['banner'] = banner.decode('utf-8', errors='ignore').strip()
            except:
                pass
            
            # 根据端口发送特定探测包
            if port == 80 or port in [8080, 8000, 3000, 5000]:
                # HTTP服务探测
                http_request = b"GET / HTTP/1.1\r\nHost: " + target.encode() + b"\r\n\r\n"
                writer.write(http_request)
                await writer.drain()
                
                response = await asyncio.wait_for(
                    reader.read(2048), timeout=3
                )
                if response:
                    response_str = response.decode('utf-8', errors='ignore')
                    # 提取服务器信息
                    if 'Server:' in response_str:
                        server_line = [line for line in response_str.split('\n') 
                                     if line.startswith('Server:')]
                        if server_line:
                            version_info['version'] = server_line[0].replace('Server:', '').strip()
            
            writer.close()
            await writer.wait_closed()
            
            return version_info
            
        except Exception as e:
            return {'version': '', 'banner': '', 'error': str(e)}
