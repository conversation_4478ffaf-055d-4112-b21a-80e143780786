#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务扫描模块
识别和分析Web应用程序
"""

import asyncio
import aiohttp
import ssl
import random
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
import re


class WebScanner:
    """Web服务扫描器"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        
        # 常见Web路径
        self.common_paths = [
            '/', '/admin', '/login', '/index.php', '/index.jsp',
            '/manager', '/console', '/dashboard', '/api',
            '/upload', '/uploads', '/files', '/backup',
            '/config', '/test', '/demo', '/phpinfo.php',
            '/info.php', '/status', '/health', '/debug'
        ]
        
        # 敏感文件路径
        self.sensitive_files = [
            '/web.config', '/.env', '/config.php', '/database.php',
            '/wp-config.php', '/config.inc.php', '/.htaccess',
            '/robots.txt', '/sitemap.xml', '/crossdomain.xml',
            '/phpinfo.php', '/info.php', '/test.php'
        ]
        
        # Web应用指纹
        self.fingerprints = {
            'Apache': [r'Apache/[\d.]+', r'Server: Apache'],
            'Nginx': [r'nginx/[\d.]+', r'Server: nginx'],
            'IIS': [r'Microsoft-IIS/[\d.]+', r'Server: Microsoft-IIS'],
            'Tomcat': [r'Apache-Coyote/[\d.]+', r'Tomcat'],
            'PHP': [r'X-Powered-By: PHP/[\d.]+', r'Set-Cookie: PHPSESSID'],
            'ASP.NET': [r'X-AspNet-Version', r'X-Powered-By: ASP.NET'],
            'JSP': [r'Set-Cookie: JSESSIONID', r'X-Powered-By: Servlet'],
            'WordPress': [r'wp-content', r'wp-includes', r'/wp-admin/'],
            'Drupal': [r'Drupal.settings', r'/sites/default/'],
            'Joomla': [r'/components/', r'/modules/', r'Joomla'],
            'Laravel': [r'laravel_session', r'X-Powered-By: Laravel'],
            'Spring': [r'Spring Framework', r'Whitelabel Error Page']
        }
    
    async def scan_target(self, target: str, ports: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """扫描目标的Web服务"""
        web_services = []
        
        # 创建HTTP客户端
        connector = aiohttp.TCPConnector(
            ssl=ssl.create_default_context(),
            verify_ssl=False,
            limit=50
        )
        
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': random.choice(self.config.user_agents)}
        ) as session:
            
            # 扫描每个Web端口
            for port_info in ports:
                port = port_info['port']
                
                # 确定协议
                if port in [443, 8443] or 'https' in port_info.get('service', ''):
                    protocols = ['https']
                else:
                    protocols = ['http', 'https']  # 尝试两种协议
                
                for protocol in protocols:
                    try:
                        base_url = f"{protocol}://{target}:{port}"
                        web_info = await self._scan_web_service(session, base_url)
                        
                        if web_info:
                            web_info.update({
                                'target': target,
                                'port': port,
                                'protocol': protocol,
                                'base_url': base_url
                            })
                            web_services.append(web_info)
                            break  # 成功后不再尝试其他协议
                            
                    except Exception as e:
                        self.logger.debug(f"扫描 {protocol}://{target}:{port} 失败: {e}")
        
        self.logger.debug(f"{target} 发现 {len(web_services)} 个Web服务")
        return web_services
    
    async def _scan_web_service(self, session: aiohttp.ClientSession, 
                               base_url: str) -> Optional[Dict[str, Any]]:
        """扫描单个Web服务"""
        try:
            # 发送GET请求到根路径
            async with session.get(base_url) as response:
                content = await response.text()
                headers = dict(response.headers)
                
                web_info = {
                    'status_code': response.status,
                    'headers': headers,
                    'title': self._extract_title(content),
                    'server': headers.get('Server', ''),
                    'technologies': self._identify_technologies(headers, content),
                    'paths': [],
                    'sensitive_files': [],
                    'forms': self._extract_forms(content),
                    'links': self._extract_links(content, base_url)
                }
                
                # 路径扫描
                if self.config.exploit_mode:
                    paths = await self._scan_paths(session, base_url)
                    web_info['paths'] = paths
                    
                    # 敏感文件扫描
                    sensitive = await self._scan_sensitive_files(session, base_url)
                    web_info['sensitive_files'] = sensitive
                
                return web_info
                
        except Exception as e:
            self.logger.debug(f"扫描Web服务 {base_url} 失败: {e}")
            return None
    
    def _extract_title(self, content: str) -> str:
        """提取页面标题"""
        try:
            match = re.search(r'<title[^>]*>(.*?)</title>', content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()
        except:
            pass
        return ''
    
    def _identify_technologies(self, headers: Dict[str, str], content: str) -> List[str]:
        """识别Web技术栈"""
        technologies = []
        
        # 检查HTTP头
        header_text = ' '.join([f"{k}: {v}" for k, v in headers.items()])
        
        # 检查内容
        full_text = header_text + ' ' + content
        
        for tech, patterns in self.fingerprints.items():
            for pattern in patterns:
                if re.search(pattern, full_text, re.IGNORECASE):
                    if tech not in technologies:
                        technologies.append(tech)
                    break
        
        return technologies
    
    def _extract_forms(self, content: str) -> List[Dict[str, Any]]:
        """提取表单信息"""
        forms = []
        try:
            form_pattern = r'<form[^>]*>(.*?)</form>'
            form_matches = re.findall(form_pattern, content, re.IGNORECASE | re.DOTALL)
            
            for form_content in form_matches:
                # 提取action
                action_match = re.search(r'action=["\']([^"\']*)["\']', form_content, re.IGNORECASE)
                action = action_match.group(1) if action_match else ''
                
                # 提取method
                method_match = re.search(r'method=["\']([^"\']*)["\']', form_content, re.IGNORECASE)
                method = method_match.group(1).upper() if method_match else 'GET'
                
                # 提取输入字段
                input_pattern = r'<input[^>]*>'
                inputs = re.findall(input_pattern, form_content, re.IGNORECASE)
                
                fields = []
                for input_tag in inputs:
                    name_match = re.search(r'name=["\']([^"\']*)["\']', input_tag, re.IGNORECASE)
                    type_match = re.search(r'type=["\']([^"\']*)["\']', input_tag, re.IGNORECASE)
                    
                    if name_match:
                        fields.append({
                            'name': name_match.group(1),
                            'type': type_match.group(1) if type_match else 'text'
                        })
                
                forms.append({
                    'action': action,
                    'method': method,
                    'fields': fields
                })
        except:
            pass
        
        return forms
    
    def _extract_links(self, content: str, base_url: str) -> List[str]:
        """提取页面链接"""
        links = []
        try:
            link_pattern = r'href=["\']([^"\']*)["\']'
            matches = re.findall(link_pattern, content, re.IGNORECASE)
            
            for link in matches:
                if link.startswith('http'):
                    links.append(link)
                elif link.startswith('/'):
                    links.append(urljoin(base_url, link))
                
                if len(links) >= 20:  # 限制链接数量
                    break
        except:
            pass
        
        return links
    
    async def _scan_paths(self, session: aiohttp.ClientSession, 
                         base_url: str) -> List[Dict[str, Any]]:
        """扫描常见路径"""
        found_paths = []
        
        for path in self.common_paths:
            try:
                url = urljoin(base_url, path)
                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        found_paths.append({
                            'path': path,
                            'url': url,
                            'status_code': response.status,
                            'title': self._extract_title(content),
                            'size': len(content)
                        })
                    elif response.status in [301, 302]:
                        location = response.headers.get('Location', '')
                        found_paths.append({
                            'path': path,
                            'url': url,
                            'status_code': response.status,
                            'redirect': location
                        })
                        
            except Exception as e:
                self.logger.debug(f"扫描路径 {path} 失败: {e}")
                
            # 隐蔽模式下添加延迟
            if self.config.stealth_mode:
                await asyncio.sleep(random.uniform(0.1, 0.5))
        
        return found_paths
    
    async def _scan_sensitive_files(self, session: aiohttp.ClientSession, 
                                   base_url: str) -> List[Dict[str, Any]]:
        """扫描敏感文件"""
        found_files = []
        
        for file_path in self.sensitive_files:
            try:
                url = urljoin(base_url, file_path)
                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        found_files.append({
                            'path': file_path,
                            'url': url,
                            'status_code': response.status,
                            'size': len(content),
                            'content_preview': content[:200] + '...' if len(content) > 200 else content
                        })
                        
            except Exception as e:
                self.logger.debug(f"扫描敏感文件 {file_path} 失败: {e}")
                
            # 隐蔽模式下添加延迟
            if self.config.stealth_mode:
                await asyncio.sleep(random.uniform(0.1, 0.5))
        
        return found_files

    async def deep_scan(self, session: aiohttp.ClientSession,
                       base_url: str) -> Dict[str, Any]:
        """深度扫描Web应用"""
        deep_info = {
            'cms_detection': await self._detect_cms(session, base_url),
            'admin_panels': await self._scan_admin_panels(session, base_url),
            'backup_files': await self._scan_backup_files(session, base_url),
            'config_files': await self._scan_config_files(session, base_url)
        }
        return deep_info

    async def _detect_cms(self, session: aiohttp.ClientSession, base_url: str) -> Dict[str, str]:
        """检测CMS类型和版本"""
        cms_info = {'type': '', 'version': ''}

        try:
            # WordPress检测
            wp_paths = ['/wp-admin/', '/wp-content/', '/wp-includes/']
            for path in wp_paths:
                url = urljoin(base_url, path)
                async with session.get(url) as response:
                    if response.status == 200:
                        cms_info['type'] = 'WordPress'
                        # 尝试获取版本
                        readme_url = urljoin(base_url, '/readme.html')
                        try:
                            async with session.get(readme_url) as readme_resp:
                                if readme_resp.status == 200:
                                    content = await readme_resp.text()
                                    version_match = re.search(r'Version (\d+\.\d+(?:\.\d+)?)', content)
                                    if version_match:
                                        cms_info['version'] = version_match.group(1)
                        except:
                            pass
                        break

            # Drupal检测
            if not cms_info['type']:
                drupal_paths = ['/sites/default/', '/modules/', '/themes/']
                for path in drupal_paths:
                    url = urljoin(base_url, path)
                    async with session.get(url) as response:
                        if response.status == 200:
                            cms_info['type'] = 'Drupal'
                            break

            # Joomla检测
            if not cms_info['type']:
                joomla_paths = ['/administrator/', '/components/', '/modules/']
                for path in joomla_paths:
                    url = urljoin(base_url, path)
                    async with session.get(url) as response:
                        if response.status == 200:
                            cms_info['type'] = 'Joomla'
                            break

        except Exception as e:
            self.logger.debug(f"CMS检测失败: {e}")

        return cms_info

    async def _scan_admin_panels(self, session: aiohttp.ClientSession, base_url: str) -> List[str]:
        """扫描管理面板"""
        admin_paths = [
            '/admin', '/administrator', '/admin.php', '/admin/',
            '/wp-admin/', '/manager/', '/console/', '/control/',
            '/dashboard/', '/backend/', '/cpanel/', '/webadmin/',
            '/sysadmin/', '/system/', '/manage/', '/management/'
        ]

        found_panels = []
        for path in admin_paths:
            try:
                url = urljoin(base_url, path)
                async with session.get(url) as response:
                    if response.status in [200, 401, 403]:
                        found_panels.append(url)
            except:
                pass

        return found_panels

    async def _scan_backup_files(self, session: aiohttp.ClientSession, base_url: str) -> List[str]:
        """扫描备份文件"""
        backup_extensions = ['.bak', '.backup', '.old', '.orig', '.tmp', '.zip', '.tar.gz', '.sql']
        common_files = ['index', 'config', 'database', 'backup', 'dump', 'data']

        found_backups = []
        for file_name in common_files:
            for ext in backup_extensions:
                try:
                    url = urljoin(base_url, f"/{file_name}{ext}")
                    async with session.head(url) as response:
                        if response.status == 200:
                            found_backups.append(url)
                except:
                    pass

        return found_backups

    async def _scan_config_files(self, session: aiohttp.ClientSession, base_url: str) -> List[str]:
        """扫描配置文件"""
        config_files = [
            '/.env', '/config.php', '/configuration.php', '/settings.php',
            '/config.inc.php', '/config.xml', '/web.config', '/app.config',
            '/database.yml', '/config.yml', '/application.yml'
        ]

        found_configs = []
        for config_file in config_files:
            try:
                url = urljoin(base_url, config_file)
                async with session.get(url) as response:
                    if response.status == 200:
                        found_configs.append(url)
            except:
                pass

        return found_configs
