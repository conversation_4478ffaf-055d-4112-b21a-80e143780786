# GetShell - 全域扫描工具

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Version](https://img.shields.io/badge/Version-1.0.0-red.svg)

专业的红队渗透测试工具，用于批量获取webshell。

## ⚠️ 免责声明

**本工具仅供授权的安全测试使用！**

- 请确保您有合法的授权才能使用此工具进行测试
- 未经授权的使用可能违反相关法律法规
- 使用者需要对自己的行为负责
- 本工具开发者不承担任何法律责任

## 🎯 功能特性

- ✅ **高效异步扫描**: 基于asyncio的高性能异步扫描引擎
- ✅ **智能端口扫描**: 支持TCP端口扫描和服务识别
- ✅ **Web服务识别**: 自动识别Web应用和技术栈
- ✅ **漏洞检测**: 支持SQL注入、XSS、命令注入、文件包含等漏洞检测
- ✅ **自动化利用**: 自动利用漏洞获取Webshell
- ✅ **多种Webshell**: 支持PHP、JSP、ASPX等多种类型
- ✅ **隐蔽扫描**: 支持隐蔽模式，避免被检测
- ✅ **详细报告**: 生成JSON、XML、HTML、TXT、CSV等多种格式报告
- ✅ **目标解析**: 支持IP、CIDR、域名等多种目标格式

## 📦 安装

### 环境要求

- Python 3.7+
- Linux/macOS/Windows

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/your-repo/getshell.git
cd getshell
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 运行工具
```bash
python main.py -h
```

## 🚀 使用方法

### 基本用法

```bash
# 扫描单个目标
python main.py -t ***********00

# 扫描网段
python main.py -t ***********/24

# 从文件读取目标
python main.py -f targets.txt

# 指定端口
python main.py -t ***********00 -p 80,443,8080

# 启用漏洞利用模式
python main.py -t ***********00 --exploit-mode
```

### 高级用法

```bash
# 全端口扫描
python main.py -t ***********00 --full-scan

# 隐蔽扫描模式
python main.py -t ***********00 --stealth

# 指定线程数
python main.py -t ***********/24 --threads 100

# 指定输出格式
python main.py -t ***********00 --format html

# 详细输出
python main.py -t ***********00 -v

# 调试模式
python main.py -t ***********00 --debug
```

### 参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `-t, --target` | 目标IP/域名/CIDR | `-t ***********` |
| `-f, --file` | 目标文件路径 | `-f targets.txt` |
| `-p, --ports` | 端口列表 | `-p 80,443,8080` |
| `--threads` | 并发线程数 | `--threads 50` |
| `--timeout` | 连接超时时间 | `--timeout 10` |
| `--full-scan` | 全端口扫描 | `--full-scan` |
| `--fast-scan` | 快速扫描模式 | `--fast-scan` |
| `--stealth` | 隐蔽扫描模式 | `--stealth` |
| `--exploit-mode` | 启用漏洞利用 | `--exploit-mode` |
| `--webshell` | Webshell类型 | `--webshell php` |
| `-o, --output` | 输出目录 | `-o results` |
| `--format` | 输出格式 | `--format html` |
| `-v, --verbose` | 详细输出 | `-v` |
| `--debug` | 调试模式 | `--debug` |

## 📊 输出格式

工具支持多种输出格式：

- **JSON**: 结构化数据，便于程序处理
- **XML**: 标准XML格式
- **HTML**: 可视化报告，支持浏览器查看
- **TXT**: 纯文本格式，便于阅读
- **CSV**: 表格格式，便于数据分析

## 🎯 Webshell类型

支持的Webshell类型：

- **PHP**: 适用于PHP环境
- **JSP**: 适用于Java Web环境
- **ASPX**: 适用于.NET环境

每种类型都提供多个模板：
- `simple`: 简单版本
- `advanced`: 高级版本（带密码保护）
- `stealth`: 隐蔽版本

## 🔧 配置文件

可以通过修改 `core/config.py` 来自定义配置：

- 默认端口列表
- User-Agent列表
- 漏洞检测载荷
- Webshell模板

## 📝 使用示例

### 示例1: 基本扫描

```bash
python main.py -t ***********00 -p 80,443,8080 --threads 50
```

### 示例2: 网段扫描

```bash
python main.py -t ***********/24 --exploit-mode --webshell php
```

### 示例3: 从文件扫描

```bash
# targets.txt 内容:
# ***********00
# ***********01
# example.com

python main.py -f targets.txt --full-scan --output results/
```

### 示例4: 隐蔽扫描

```bash
python main.py -t target.com --stealth --threads 10 --timeout 15
```

## 📁 项目结构

```
getshell/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── core/                  # 核心模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志管理
│   └── scanner.py         # 主扫描器
├── modules/               # 功能模块
│   ├── __init__.py
│   ├── port_scanner.py    # 端口扫描
│   ├── web_scanner.py     # Web扫描
│   ├── vuln_scanner.py    # 漏洞扫描
│   └── exploit_manager.py # 漏洞利用
└── utils/                 # 工具类
    ├── __init__.py
    ├── banner.py          # 横幅显示
    ├── target_parser.py   # 目标解析
    └── report_generator.py # 报告生成
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [项目主页](https://github.com/your-repo/getshell)
- [问题反馈](https://github.com/your-repo/getshell/issues)
- [更新日志](CHANGELOG.md)

---

**再次提醒：本工具仅供授权的安全测试使用，请遵守相关法律法规！**
