#!/bin/bash

# GetShell 启动脚本

# 检查Python版本
python_version=$(python3 -c "import sys; print(sys.version_info.major, sys.version_info.minor)")
major=$(echo $python_version | cut -d' ' -f1)
minor=$(echo $python_version | cut -d' ' -f2)

if [ "$major" -lt 3 ] || ([ "$major" -eq 3 ] && [ "$minor" -lt 7 ]); then
    echo "❌ 需要Python 3.7或更高版本"
    exit 1
fi

echo "✅ Python版本检查通过"

# 检查依赖
echo "🔍 检查依赖包..."
if ! python3 -c "import aiohttp, aiofiles, dns.resolver, jinja2" 2>/dev/null; then
    echo "📦 安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败"
        exit 1
    fi
fi

echo "✅ 依赖检查完成"

# 创建输出目录
if [ ! -d "results" ]; then
    mkdir -p results
    echo "📁 创建输出目录: results/"
fi

# 运行工具
echo "🚀 启动GetShell..."
python3 main.py "$@"
