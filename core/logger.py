#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理模块
统一的日志记录和输出管理
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        """格式化日志记录"""
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logger(verbose: bool = False, debug: bool = False, 
                output_dir: str = "results") -> logging.Logger:
    """设置日志记录器"""
    
    # 创建日志器
    logger = logging.getLogger('getshell')
    logger.setLevel(logging.DEBUG if debug else logging.INFO)
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    
    if debug:
        console_level = logging.DEBUG
        console_format = '[%(asctime)s] [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s'
    elif verbose:
        console_level = logging.INFO
        console_format = '[%(asctime)s] [%(levelname)s] %(message)s'
    else:
        console_level = logging.INFO
        console_format = '[%(levelname)s] %(message)s'
    
    console_handler.setLevel(console_level)
    console_formatter = ColoredFormatter(console_format, datefmt='%H:%M:%S')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        # 主日志文件
        log_file = os.path.join(output_dir, f"getshell_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # 错误日志文件
        error_file = os.path.join(output_dir, f"errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        error_handler = logging.FileHandler(error_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        logger.addHandler(error_handler)
    
    return logger


class ScanLogger:
    """扫描专用日志记录器"""
    
    def __init__(self, logger: logging.Logger, output_dir: str):
        self.logger = logger
        self.output_dir = output_dir
        self.results_file = os.path.join(output_dir, f"scan_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        
        # 创建结果文件
        with open(self.results_file, 'w', encoding='utf-8') as f:
            f.write(f"GetShell 扫描结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
    
    def log_target_start(self, target: str):
        """记录目标开始扫描"""
        msg = f"开始扫描目标: {target}"
        self.logger.info(msg)
        self._write_to_file(f"\n[{datetime.now().strftime('%H:%M:%S')}] {msg}")
    
    def log_port_open(self, target: str, port: int, service: str = ""):
        """记录开放端口"""
        service_info = f" ({service})" if service else ""
        msg = f"发现开放端口: {target}:{port}{service_info}"
        self.logger.info(msg)
        self._write_to_file(f"  ✓ {target}:{port}{service_info}")
    
    def log_vulnerability(self, target: str, vuln_type: str, details: str = ""):
        """记录发现的漏洞"""
        msg = f"发现漏洞: {target} - {vuln_type}"
        if details:
            msg += f" ({details})"
        self.logger.warning(msg)
        self._write_to_file(f"  ⚠ 漏洞: {vuln_type} - {details}")
    
    def log_webshell_success(self, target: str, shell_url: str, shell_type: str):
        """记录Webshell获取成功"""
        msg = f"Webshell获取成功: {target} -> {shell_url} ({shell_type})"
        self.logger.critical(msg)  # 使用critical级别突出显示
        self._write_to_file(f"  🎯 WEBSHELL: {shell_url} ({shell_type})")
    
    def log_error(self, target: str, error: str):
        """记录错误"""
        msg = f"扫描错误 {target}: {error}"
        self.logger.error(msg)
        self._write_to_file(f"  ✗ 错误: {error}")
    
    def _write_to_file(self, content: str):
        """写入结果文件"""
        try:
            with open(self.results_file, 'a', encoding='utf-8') as f:
                f.write(content + "\n")
        except Exception as e:
            self.logger.error(f"写入结果文件失败: {e}")
    
    def get_results_file(self) -> str:
        """获取结果文件路径"""
        return self.results_file
