#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心扫描器模块
负责协调各个扫描组件的工作
"""

import asyncio
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from .config import Config
from .logger import Scan<PERSON>ogger
from modules.port_scanner import PortScanner
from modules.web_scanner import WebScanner
from modules.vuln_scanner import VulnScanner
from modules.exploit_manager import ExploitManager
from utils.target_parser import TargetParser
from utils.report_generator import ReportGenerator


class Scanner:
    """主扫描器类"""
    
    def __init__(self, config: Config, logger):
        self.config = config
        self.logger = logger
        self.scan_logger = ScanLogger(logger, config.output_dir)
        
        # 初始化各个扫描模块
        self.port_scanner = PortScanner(config, logger)
        self.web_scanner = WebScanner(config, logger)
        self.vuln_scanner = VulnScanner(config, logger)
        self.exploit_manager = ExploitManager(config, logger)
        
        # 扫描结果
        self.results = {
            'scan_info': {
                'start_time': None,
                'end_time': None,
                'duration': 0,
                'targets_count': 0,
                'success_count': 0,
                'webshells_count': 0
            },
            'targets': []
        }
    
    async def run(self) -> Dict[str, Any]:
        """运行扫描任务"""
        start_time = time.time()
        self.results['scan_info']['start_time'] = datetime.now().isoformat()
        
        self.logger.info("=" * 60)
        self.logger.info("GetShell 全域扫描工具启动")
        self.logger.info("=" * 60)
        
        # 解析目标
        target_parser = TargetParser(self.config, self.logger)
        parsed_targets = await target_parser.parse_targets(self.config.targets)
        
        self.results['scan_info']['targets_count'] = len(parsed_targets)
        self.logger.info(f"解析到 {len(parsed_targets)} 个目标")
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(self.config.threads)
        
        # 创建扫描任务
        tasks = []
        for target in parsed_targets:
            task = asyncio.create_task(self._scan_target(semaphore, target))
            tasks.append(task)
        
        # 执行所有扫描任务
        self.logger.info(f"开始并发扫描，线程数: {self.config.threads}")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"目标 {parsed_targets[i]} 扫描失败: {result}")
            elif result:
                self.results['targets'].append(result)
                if result.get('webshells'):
                    self.results['scan_info']['webshells_count'] += len(result['webshells'])
                if result.get('status') == 'success':
                    self.results['scan_info']['success_count'] += 1
        
        # 完成扫描
        end_time = time.time()
        self.results['scan_info']['end_time'] = datetime.now().isoformat()
        self.results['scan_info']['duration'] = round(end_time - start_time, 2)
        
        # 生成报告
        await self._generate_reports()
        
        # 显示统计信息
        self._print_summary()
        
        return self.results
    
    async def _scan_target(self, semaphore: asyncio.Semaphore, target: str) -> Optional[Dict[str, Any]]:
        """扫描单个目标"""
        async with semaphore:
            try:
                self.scan_logger.log_target_start(target)
                
                target_result = {
                    'target': target,
                    'timestamp': datetime.now().isoformat(),
                    'status': 'failed',
                    'ports': [],
                    'web_services': [],
                    'vulnerabilities': [],
                    'webshells': [],
                    'errors': []
                }
                
                # 1. 端口扫描
                self.logger.debug(f"开始端口扫描: {target}")
                open_ports = await self.port_scanner.scan_target(target, self.config.ports)
                target_result['ports'] = open_ports
                
                if not open_ports:
                    self.logger.info(f"目标 {target} 没有发现开放端口")
                    return target_result
                
                # 记录开放端口
                for port_info in open_ports:
                    self.scan_logger.log_port_open(
                        target, 
                        port_info['port'], 
                        port_info.get('service', '')
                    )
                
                # 2. Web服务识别
                web_ports = [p for p in open_ports if p['port'] in self.config.web_ports]
                if web_ports:
                    self.logger.debug(f"开始Web服务识别: {target}")
                    web_services = await self.web_scanner.scan_target(target, web_ports)
                    target_result['web_services'] = web_services
                
                # 3. 漏洞扫描
                if web_services and self.config.exploit_mode:
                    self.logger.debug(f"开始漏洞扫描: {target}")
                    vulnerabilities = await self.vuln_scanner.scan_target(target, web_services)
                    target_result['vulnerabilities'] = vulnerabilities
                    
                    # 记录发现的漏洞
                    for vuln in vulnerabilities:
                        self.scan_logger.log_vulnerability(
                            target,
                            vuln['type'],
                            vuln.get('description', '')
                        )
                    
                    # 4. 漏洞利用和Webshell部署
                    if vulnerabilities:
                        self.logger.debug(f"开始漏洞利用: {target}")
                        webshells = await self.exploit_manager.exploit_target(
                            target, vulnerabilities, web_services
                        )
                        target_result['webshells'] = webshells
                        
                        # 记录获取的Webshell
                        for shell in webshells:
                            self.scan_logger.log_webshell_success(
                                target,
                                shell['url'],
                                shell['type']
                            )
                
                target_result['status'] = 'success'
                return target_result
                
            except Exception as e:
                error_msg = f"扫描目标 {target} 时发生错误: {str(e)}"
                self.logger.error(error_msg)
                self.scan_logger.log_error(target, str(e))
                
                return {
                    'target': target,
                    'timestamp': datetime.now().isoformat(),
                    'status': 'error',
                    'error': str(e),
                    'ports': [],
                    'web_services': [],
                    'vulnerabilities': [],
                    'webshells': []
                }
    
    async def _generate_reports(self):
        """生成扫描报告"""
        try:
            report_generator = ReportGenerator(self.config, self.logger)
            await report_generator.generate_reports(self.results)
            self.logger.info(f"报告已生成到: {self.config.output_dir}")
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
    
    def _print_summary(self):
        """打印扫描摘要"""
        info = self.results['scan_info']
        
        self.logger.info("=" * 60)
        self.logger.info("扫描完成 - 统计信息")
        self.logger.info("=" * 60)
        self.logger.info(f"扫描目标数量: {info['targets_count']}")
        self.logger.info(f"成功扫描数量: {info['success_count']}")
        self.logger.info(f"获取Webshell数量: {info['webshells_count']}")
        self.logger.info(f"扫描耗时: {info['duration']} 秒")
        self.logger.info(f"结果文件: {self.scan_logger.get_results_file()}")
        self.logger.info("=" * 60)
        
        # 显示获取的Webshell
        if info['webshells_count'] > 0:
            self.logger.critical("🎯 获取到的Webshell:")
            for target_result in self.results['targets']:
                for shell in target_result.get('webshells', []):
                    self.logger.critical(f"  {shell['url']} ({shell['type']})")
