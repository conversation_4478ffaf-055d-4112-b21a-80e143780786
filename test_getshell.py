#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GetShell 测试脚本
用于测试工具的基本功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config import Config
from core.logger import Logger
from core.scanner import Scanner
from utils.banner import print_banner


async def test_basic_functionality():
    """测试基本功能"""
    print_banner()
    
    # 初始化配置
    config = Config()
    config.targets = ['127.0.0.1']
    config.ports = [80, 443, 8080]
    config.threads = 10
    config.timeout = 5
    config.verbose = True
    
    # 初始化日志
    logger = Logger(config)
    
    # 初始化扫描器
    scanner = Scanner(config, logger)
    
    print("🧪 开始测试基本功能...")
    
    try:
        # 测试目标解析
        print("✅ 测试目标解析...")
        parsed_targets = await scanner.target_parser.parse_targets(config.targets)
        print(f"   解析出 {len(parsed_targets)} 个目标")
        
        # 测试端口扫描
        print("✅ 测试端口扫描...")
        if parsed_targets:
            target = parsed_targets[0]
            port_results = await scanner.port_scanner.scan_target(target, config.ports)
            print(f"   扫描 {target}，发现 {len(port_results)} 个开放端口")
        
        print("🎉 基本功能测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


async def test_webshell_templates():
    """测试Webshell模板"""
    print("\n🧪 测试Webshell模板...")
    
    config = Config()
    logger = Logger(config)
    
    from modules.exploit_manager import ExploitManager
    exploit_manager = ExploitManager(config, logger)
    
    # 测试模板
    for shell_type, templates in exploit_manager.webshell_templates.items():
        print(f"✅ {shell_type.upper()} 模板:")
        for template_name, template_content in templates.items():
            print(f"   - {template_name}: {len(template_content)} 字符")
    
    print("🎉 Webshell模板测试完成!")


def test_configuration():
    """测试配置"""
    print("\n🧪 测试配置...")
    
    config = Config()
    
    print(f"✅ 默认端口: {config.default_ports}")
    print(f"✅ 默认线程数: {config.threads}")
    print(f"✅ 默认超时: {config.timeout}")
    print(f"✅ User-Agent数量: {len(config.user_agents)}")
    print(f"✅ SQL注入载荷数量: {len(config.sql_payloads)}")
    print(f"✅ XSS载荷数量: {len(config.xss_payloads)}")
    
    print("🎉 配置测试完成!")


def test_logger():
    """测试日志功能"""
    print("\n🧪 测试日志功能...")
    
    config = Config()
    config.verbose = True
    config.debug = True
    
    logger = Logger(config)
    
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    logger.debug("这是一条调试日志")
    logger.success("这是一条成功日志")
    
    print("🎉 日志功能测试完成!")


async def main():
    """主测试函数"""
    print("=" * 60)
    print("GetShell 工具测试")
    print("=" * 60)
    
    # 测试配置
    test_configuration()
    
    # 测试日志
    test_logger()
    
    # 测试Webshell模板
    await test_webshell_templates()
    
    # 测试基本功能
    await test_basic_functionality()
    
    print("\n" + "=" * 60)
    print("所有测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
